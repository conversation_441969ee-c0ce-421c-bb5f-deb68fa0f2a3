interface ErrorPageProps {
  errorCode?: string;
  message?: string;
}

const ErrorPage: React.FC<ErrorPageProps> = ({
  errorCode = '',
  message = 'We are updating our servers. Please try again later.',
}) => {
  return (
    <div className='flex items-center justify-center w-full h-screen bg-gray-100'>
      <div className='text-center'>
        <h1 className='text-6xl font-bold text-gray-800'>{errorCode}</h1>
        <p className='text-gray-600'>{message}</p>
      </div>
    </div>
  );
};

export default ErrorPage;
