import { httpsCallable } from 'firebase/functions';
import { AppProposal, AppProposalStatus } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  setDoc,
  where,
} from 'firebase/firestore';
import { toast } from 'sonner';
import { firebaseFunctions, firestore } from '../services/firebase-service';
import { getCurrentUserId } from './auth.api';

// for admin we use this
export async function getProposals(): Promise<AppProposal[]> {
  try {
    const q = query(collection(firestore, 'proposal'));

    const proposalSnapshot = await getDocs(q);
    const proposals: AppProposal[] = [];
    proposalSnapshot.forEach((doc) => {
      proposals.push({ id: doc.id, ...doc.data() } as AppProposal);
    });
    return proposals;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving proposals list:', error);
    return [];
  }
}

// Proposals:
export const createProposal = async (proposalData: AppProposal) => {
  const createProposal = httpsCallable(firebaseFunctions, 'createProposal');

  try {
    const proposerId = getCurrentUserId();
    await createProposal({
      ...proposalData,
      proposerId,
      status: AppProposalStatus.Pending,
    });
    toast.success('Proposal created!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error creating proposal:', error);
  }
};

export const updateProposal = async (proposal: AppProposal) => {
  try {
    const proposalRef = doc(collection(firestore, 'proposal'), proposal.id);
    await setDoc(
      proposalRef,
      {
        ...proposal,
      },
      { merge: true },
    );
    toast.success('Proposal updated!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error updating proposal:', error);
  }
};

// for regular user
export const getCurrentUserProposals = async (): Promise<AppProposal[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return [];
    }

    const q = query(
      collection(firestore, 'proposal'),
      where('proposerId', '==', userId),
    );

    const proposalSnapshot = await getDocs(q);
    const proposals: AppProposal[] = [];
    proposalSnapshot.forEach((doc) => {
      proposals.push({ id: doc.id, ...doc.data() } as AppProposal);
    });
    return proposals;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving proposals list:', error);
    return [];
  }
};

export const deleteProposal = async (proposalId: string) => {
  try {
    // Delete event document in Firestore
    const eventRef = doc(collection(firestore, 'proposal'), proposalId);
    await deleteDoc(eventRef);
    toast.success('Proposal deleted!');

    // Delete corresponding file in storage
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error deleting proposal:', error);
  }
};

export const rejectProposal = async (proposalId: string, reason: string) => {
  try {
    const eventRef = doc(collection(firestore, 'proposal'), proposalId);
    await setDoc(
      eventRef,
      {
        status: AppProposalStatus.Rejected,
        rejectReason: reason,
      },
      { merge: true },
    );
    toast.success('Proposal rejected!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error rejecting proposal:', error);
  }
};
