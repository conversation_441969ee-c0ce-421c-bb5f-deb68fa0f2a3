import { AppResource, AppBookmark } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  query,
  setDoc,
  where,
} from 'firebase/firestore';
import { toast } from 'sonner';
import { firestore } from '../services/firebase-service';
import { getCurrentUserId } from './auth.api';
import { RESOURCE_COLLECTION } from './app-resource.api';

export const getAllUserBookmarks = async (): Promise<AppBookmark[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return [];
    }

    const bookmarkRef = collection(firestore, 'bookmark');

    const q = query(bookmarkRef, where('userId', '==', userId));

    const bookmarkSnapshot = await getDocs(q);
    const bookmarks: AppBookmark[] = [];
    bookmarkSnapshot.forEach((doc) => {
      bookmarks.push({ id: doc.id, ...doc.data() } as AppBookmark);
    });

    return bookmarks;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving app resource list:', error);
    return [];
  }
};

export const getBookmarkedAppResources = async (bookmarks: AppBookmark[]) => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { appResources: [], cursor: null };
    }

    const appResources: AppResource[] = [];
    for (const bookmark of bookmarks) {
      const appResourceSnapshot = await getDoc(
        doc(collection(firestore, RESOURCE_COLLECTION), bookmark.resourceId),
      );
      appResources.push({
        id: appResourceSnapshot.id,
        ...appResourceSnapshot.data(),
      } as AppResource);
    }

    return {
      appResources,
      cursor: bookmarks[bookmarks.length - 1]?.id,
    };
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving resource list:', error);
    return { appResources: [], cursor: null };
  }
};

export const addBookmark = async (resourceId: string) => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return;
    }

    const q = query(
      collection(firestore, 'bookmark'),
      where('userId', '==', userId),
      where('resourceId', '==', resourceId),
    );
    const bookmarkSnapshot = await getDocs(q);
    const bookmark: AppBookmark[] = [];
    bookmarkSnapshot.forEach((doc) => {
      bookmark.push({ id: doc.id, ...doc.data() } as AppBookmark);
    });
    if (bookmark.length > 0) {
      toast.error('You already bookmarked this resource!');
      return;
    }

    const bookmarkRef = collection(firestore, 'bookmark');
    const docRef = doc(bookmarkRef);
    await setDoc(docRef, {
      userId,
      resourceId,
    });
    toast.success('Bookmark added!');
    return docRef.id;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error bookmarking app resource:', error);
  }
};

export const removeBookmark = async (resourceId: string) => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return;
    }

    const q = query(
      collection(firestore, 'bookmark'),
      where('userId', '==', userId),
      where('resourceId', '==', resourceId),
    );
    const bookmarkSnapshot = await getDocs(q);
    const bookmark: AppBookmark[] = [];
    bookmarkSnapshot.forEach((doc) => {
      bookmark.push({ id: doc.id, ...doc.data() } as AppBookmark);
    });

    const bookmarkRef = doc(collection(firestore, 'bookmark'), bookmark[0].id);
    await deleteDoc(bookmarkRef);
    toast.success('Bookmark removed!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error unbookmarking resource:', error);
  }
};
