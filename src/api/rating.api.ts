import { httpsCallable } from 'firebase/functions';
import { RatingItem } from '../core.types';

import { collection, getDocs, query, where } from 'firebase/firestore';
import { toast } from 'sonner';
import { firebaseFunctions, firestore } from '../services/firebase-service';
import { getCurrentUserId } from './auth.api';

export const rateAppResource = async (rateData: Partial<RatingItem>) => {
  const rateAction = httpsCallable(firebaseFunctions, 'rateAppResource');

  try {
    const userId = getCurrentUserId();
    await rateAction({
      ...rateData,
      userId,
    });
    toast.success('Resource rated!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error rating resource:', error);
    throw error;
  }
};

export const getRatingRecord = async (
  resourceId: string,
): Promise<RatingItem | null> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      return null;
    }

    const q = query(
      collection(firestore, 'rating'),
      where('userId', '==', userId),
      where('resourceId', '==', resourceId),
    );
    const ratingSnapshot = await getDocs(q);
    const rating: RatingItem[] = [];

    ratingSnapshot.forEach((doc) => {
      rating.push({ id: doc.id, ...doc.data() } as RatingItem);
    });

    return rating[0] || null;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving rating:', error);
    return null;
  }
};
