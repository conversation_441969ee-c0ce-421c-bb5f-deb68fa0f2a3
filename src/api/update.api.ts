import { UpdateType } from '../core.types';

import {
  collection,
  doc,
  getDocs,
  query,
  setDoc,
  where,
} from 'firebase/firestore';
import { toast } from 'sonner';
import { firestore } from '../services/firebase-service';
import {
  setAuthorsUpdateIdInLocalStorage,
  setTagsUpdateIdInLocalStorage,
} from '../services/utils-service';

export const changeUpdateRecord = async (type: UpdateType) => {
  const updateRef = collection(firestore, 'update');

  const q = query(updateRef, where('type', '==', type));
  const updateSnapshot = await getDocs(q);
  const update: any[] = [];
  updateSnapshot.forEach((doc) => {
    update.push({ id: doc.id, ...doc.data() });
  });
  const id = update[0].id;
  const updateDocRef = doc(updateRef, id);
  await setDoc(updateDocRef, {
    updateId: Math.random().toString(36).substr(2, 9),
    type: type,
  });
};

export const isFreshUpdateAvailable = async (
  currentUpdateTag,
  updateType: UpdateType,
): Promise<boolean> => {
  try {
    const q = query(
      collection(firestore, 'update'),
      where('updateId', '!=', currentUpdateTag),
      where('type', '==', updateType),
    );
    const updateSnapshot = await getDocs(q);
    const update: any[] = [];

    updateSnapshot.forEach((doc) => {
      update.push({ id: doc.id, ...doc.data() });
    });

    const isAvailable = update.length > 0;

    if (isAvailable) {
      updateType == UpdateType.Tag
        ? setTagsUpdateIdInLocalStorage(update[0].updateId)
        : setAuthorsUpdateIdInLocalStorage(update[0].updateId);
    }

    return isAvailable;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving update:', error);
    return false;
  }
};
