import { collection, query, getCountFromServer } from 'firebase/firestore';
import { firestore } from '../services/firebase-service';
import { AUTHOR_COLLECTION } from './authors.api';
import { RESOURCE_COLLECTION } from './app-resource.api';

export interface AdminCounters {
  tags: number;
  authors: number;
  resources: number;
}

/**
 * Get count of documents in the tags collection
 */
export const getTagsCount = async (): Promise<number> => {
  try {
    const tagsCollection = collection(firestore, 'tag');
    const tagsQuery = query(tagsCollection);
    const snapshot = await getCountFromServer(tagsQuery);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting tags count:', error);
    return 0;
  }
};

/**
 * Get count of documents in the authors collection
 */
export const getAuthorsCount = async (): Promise<number> => {
  try {
    const authorsCollection = collection(firestore, AUTHOR_COLLECTION);
    const authorsQuery = query(authorsCollection);
    const snapshot = await getCountFromServer(authorsQuery);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting authors count:', error);
    return 0;
  }
};

/**
 * Get count of documents in the resources collection
 */
export const getResourcesCount = async (): Promise<number> => {
  try {
    const resourcesCollection = collection(firestore, RESOURCE_COLLECTION);
    const resourcesQuery = query(resourcesCollection);
    const snapshot = await getCountFromServer(resourcesQuery);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting resources count:', error);
    return 0;
  }
};

/**
 * Get all admin counters in a single call
 */
export const getAllAdminCounters = async (): Promise<AdminCounters> => {
  try {
    const [tagsCount, authorsCount, resourcesCount] = await Promise.all([
      getTagsCount(),
      getAuthorsCount(),
      getResourcesCount(),
    ]);

    return {
      tags: tagsCount,
      authors: authorsCount,
      resources: resourcesCount,
    };
  } catch (error) {
    console.error('Error getting admin counters:', error);
    return {
      tags: 0,
      authors: 0,
      resources: 0,
    };
  }
};
