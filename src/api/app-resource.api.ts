import { AppResource, AppResourceType } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  startAfter,
  where,
} from 'firebase/firestore';
import { toast } from 'sonner';
import { firestore } from '../services/firebase-service';
import { formatDateToFirebaseTimestamp } from '../services/utils-service';

type GetAppResourceListDTO = {
  cursor?: string | null;
  search?: string;
  authorId?: string;
  resourceType?: AppResourceType;
  tags: string[];
  searchLimit: number;
};

export const RESOURCE_COLLECTION = 'article';

export const createAppResource = async (appResourceData: AppResource) => {
  try {
    const appResourceRef = collection(firestore, RESOURCE_COLLECTION);
    const docRef = doc(appResourceRef);
    await setDoc(docRef, {
      ...appResourceData,
      authorId: appResourceData.authorId || '',
      likesCount: 0,
      dislikesCount: 0,
      createdAt: formatDateToFirebaseTimestamp(
        appResourceData.createdAt as string,
      ),
    });
    toast.success('App resource created!');
    return docRef.id;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error creating app resource:', error);
  }
};

export const updateAppResource = async (appResourceData: AppResource) => {
  try {
    const appResourceRef = doc(
      collection(firestore, RESOURCE_COLLECTION),
      appResourceData.id,
    );
    await setDoc(
      appResourceRef,
      {
        ...appResourceData,
        createdAt: formatDateToFirebaseTimestamp(
          appResourceData.createdAt as string,
        ),
      },
      { merge: true },
    );
    toast.success('App resource updated!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error updating app resource:', error);
  }
};

export const deleteAppResource = async (appResourceId: string) => {
  try {
    // Delete event document in Firestore
    const appResourceRef = doc(
      collection(firestore, RESOURCE_COLLECTION),
      appResourceId,
    );
    await deleteDoc(appResourceRef);
    toast.success('App resource deleted!');
    // Delete corresponding file in storage
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error deleting App resource:', error);
  }
};

export const getAppResourceList = async ({
  cursor,
  search,
  authorId,
  tags,
  resourceType,
  searchLimit,
}: GetAppResourceListDTO): Promise<AppResource[]> => {
  try {
    const appResourceRef = collection(firestore, RESOURCE_COLLECTION);

    const lastDocSnapshot = cursor
      ? await getDoc(doc(appResourceRef, cursor))
      : null;

    const q = query(
      appResourceRef,
      ...(search
        ? [
            where('title', '>=', search),
            where('title', '<=', search + '\uf8ff'),
          ]
        : []),
      ...(tags.length > 0 ? [where('tags', 'array-contains-any', tags)] : []),
      ...(authorId ? [where('authorId', '==', authorId)] : []),
      ...[where('type', '==', resourceType ?? AppResourceType.Article)],
      limit(searchLimit),
      ...(search
        ? [orderBy('title'), orderBy('createdAt', 'desc')]
        : [orderBy('createdAt', 'desc')]),
      ...(cursor && lastDocSnapshot ? [startAfter(lastDocSnapshot)] : []),
    );
    const appResourceSnapshot = await getDocs(q);
    const appResources: AppResource[] = [];

    appResourceSnapshot.forEach((doc) => {
      appResources.push({ id: doc.id, ...doc.data() } as AppResource);
    });

    return appResources;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving app resources list:', error);
    return [];
  }
};
