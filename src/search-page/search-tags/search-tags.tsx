import { useRootContext } from '@/context/useRootContext';
import { useMemo } from 'react';
import { AppTag, TagGroup, TagGroupName } from '../../core.types';
import { enumToArray } from '../../services/utils-service';
import { useAppResourcesContext } from '../app-resources-context';
import { useSearchFiltersContext } from '../search-filters-context';
import { SearchTag } from './search-tag';

const TAG_SELECTION_LIMIT = 10;

export const SearchTags = ({ style }: { style: React.CSSProperties }) => {
  const { tags } = useRootContext();
  const { selectedTagIds, setSelectedTagIds } = useSearchFiltersContext();
  const { resetAndReloadResources } = useAppResourcesContext();

  const toggleTag = (tag: string) => {
    setSelectedTagIds(
      selectedTagIds.includes(tag)
        ? selectedTagIds.filter((t) => t !== tag)
        : selectedTagIds.length < TAG_SELECTION_LIMIT
        ? [...selectedTagIds, tag]
        : selectedTagIds,
    );
    resetAndReloadResources();
  };

  const groupedTags: Record<TagGroup, AppTag[]> = useMemo(
    () =>
      enumToArray(TagGroup).reduce((acc, tagGroup) => {
        acc[tagGroup] = tags.filter((tag) => tag.group === tagGroup);
        return acc;
      }, {}),
    [tags],
  );

  return (
    <div className='flex flex-col gap-4 p-3 text-left text-sm ' style={style}>
      {Object.entries(groupedTags).map(
        ([groupName, tags]: [string, AppTag[]], i) => (
          <SearchTagGroup
            key={i}
            {...{
              groupName: groupName as TagGroup,
              tags,
              toggleTag,
              selectedTagIds,
            }}
          />
        ),
      )}
    </div>
  );
};

type SearchTagGroupProps = {
  groupName: TagGroup;
  tags: AppTag[];
  selectedTagIds: string[];
  toggleTag: (id: string) => void;
};

export const SearchTagGroup = ({
  groupName,
  tags,
  selectedTagIds,
  toggleTag,
}: SearchTagGroupProps) => {
  const renderTag = (tag: AppTag) => {
    const isTagSelected = selectedTagIds.includes(tag.id);

    const onClick = () => toggleTag(tag.id);

    return (
      <SearchTag
        {...{
          tag,
          isTagSelected,
          onClick,
        }}
      />
    );
  };

  if (!tags.length) return null;

  return (
    <div>
      <div className='font-bold mb-2 text-left flex justify-between items-center w-full'>
        {TagGroupName[groupName]}
      </div>
      <div className='w-[95%] flex gap-2 flex-wrap'>{tags.map(renderTag)}</div>
    </div>
  );
};
