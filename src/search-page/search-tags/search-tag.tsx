import { Badge } from '@/components/ui/badge';
import { AppTag } from '@/core.types';
import c from 'classnames';

export const SearchTag = ({
  tag,
  isTagSelected,
  onClick,
}: {
  tag: AppTag;
  onClick?: () => void;
  isTagSelected: boolean;
}) => {
  return (
    <Badge
      key={tag.name}
      className={c(onClick ? 'cursor-pointer' : '', 'hover:text-black', {
        'bg-yellow-300 text-black dark:bg-yellow-300 dark:text-black hover:bg-yellow-300':
          isTagSelected,
        'border border-slate-400 bg-transparent text-slate-400 hover:text-white dark:hover:text-black':
          !isTagSelected,
      })}
      onClick={onClick}
    >
      {tag.name}
    </Badge>
  );
};
