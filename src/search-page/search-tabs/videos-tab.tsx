import { VideoCard } from './video-card';
import { useAppResourcesContext } from '../app-resources-context';
import { ResourceTable } from '@/components/ResourceTable';
import { useGlobalView } from '@/context/GlobalViewContext';
import { VideoCardSkeleton } from '@/components/skeletons/CardSkeletons';
import { GridSkeleton } from '@/components/skeletons/TableSkeletons';

export const VideoTab = () => {
  const { loadingMarkup, resources, loading } = useAppResourcesContext();
  const { globalViewMode } = useGlobalView();

  return (
    <div style={{ height: 'calc(100vh - 117px)', overflowY: 'auto' }}>
      {globalViewMode === 'table' ? (
        <div className='p-4'>
          <ResourceTable
            resources={resources}
            loading={loading}
            loadingMarkup={loadingMarkup}
            showViewToggle={false}
          />
        </div>
      ) : (
        <div className='max-w-7xl mx-auto p-2'>
          {loading && resources.length === 0 ? (
            <GridSkeleton
              SkeletonComponent={VideoCardSkeleton}
              count={4}
              columns='grid-cols-1 sm:grid-cols-2'
            />
          ) : (
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-2'>
              {resources.map((resource) => (
                <VideoCard
                  key={resource.id}
                  {...{
                    resource,
                  }}
                />
              ))}
            </div>
          )}
          {loadingMarkup}
        </div>
      )}
    </div>
  );
};
