import { useAppResourcesContext } from '../app-resources-context';
import { GithubCard } from './github-card';
import { GithubCardSkeleton } from '@/components/skeletons/CardSkeletons';
import { GridSkeleton } from '@/components/skeletons/TableSkeletons';

export const GithubTab = () => {
  const { loadingMarkup, resources, loading } = useAppResourcesContext();

  return (
    <div className='max-w-7xl mx-auto p-2'>
      {loading && resources.length === 0 ? (
        <GridSkeleton
          SkeletonComponent={GithubCardSkeleton}
          count={6}
          columns='grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
        />
      ) : (
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
          {resources.map((resource) => (
            <GithubCard
              key={resource.id}
              {...{
                resource,
              }}
            />
          ))}
        </div>
      )}
      {loadingMarkup}
    </div>
  );
};
