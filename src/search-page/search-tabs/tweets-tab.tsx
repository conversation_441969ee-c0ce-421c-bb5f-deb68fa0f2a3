'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TweetCard } from './tweet-card';
import { useAppResourcesContext } from '../app-resources-context';
import { cn } from '@/utils';
import { TweetCardSkeleton } from '@/components/skeletons/CardSkeletons';

// Variants for animation
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring' as const,
      stiffness: 50,
      damping: 10,
    },
  },
};

// Masonry grid component
const MasonryGrid = ({
  children,
  columns = 3,
  spacing = 'md',
}: {
  children: React.ReactNode[];
  columns?: 1 | 2 | 3 | 4 | 5;
  spacing?: 'sm' | 'md' | 'lg';
}) => {
  const [columnItems, setColumnItems] = useState<React.ReactNode[][]>(
    Array(columns)
      .fill(null)
      .map(() => []),
  );

  const spacingValues = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  };

  useEffect(() => {
    // Reset column heights and items
    const heights = Array(columns).fill(0);
    const items: React.ReactNode[][] = Array(columns)
      .fill(null)
      .map(() => []);

    // Distribute children among columns based on height
    children.forEach((child, index) => {
      // Find column with the smallest height
      const shortestColumnIndex = heights.indexOf(Math.min(...heights));

      // Add child to that column
      items[shortestColumnIndex].push(
        <div
          key={`masonry-item-${index}`}
          className={cn(
            'break-inside-avoid w-full overflow-hidden',
            spacing === 'sm' ? 'mb-2' : spacing === 'md' ? 'mb-4' : 'mb-6',
          )}
        >
          {child}
        </div>,
      );

      // Update column height (simplified approximation)
      heights[shortestColumnIndex] += 1;
    });

    setColumnItems(items);
  }, [children, columns, spacing]);

  return (
    <div className={cn('grid', columnClasses[columns], spacingValues[spacing])}>
      {columnItems.map((columnChildren, columnIndex) => (
        <div key={`column-${columnIndex}`} className='flex flex-col'>
          {columnChildren}
        </div>
      ))}
    </div>
  );
};

export const TweetsTab = () => {
  const { loadingMarkup, resources } = useAppResourcesContext();
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Simulate initial loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Determine number of columns based on screen size
  const getColumnCount = (): 1 | 2 | 3 => {
    if (typeof window === 'undefined') return 3;

    const width = window.innerWidth;
    if (width < 640) return 1;
    if (width < 1024) return 2;
    return 3;
  };

  const [columnCount, setColumnCount] = useState<1 | 2 | 3>(getColumnCount());

  // Update column count on window resize
  useEffect(() => {
    const handleResize = () => {
      setColumnCount(getColumnCount());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isLoading = false; // You can connect this to actual loading state

  return (
    <div
      className='w-full max-w-7xl mx-auto px-4 py-6'
      style={{ height: 'calc(100vh - 117px)', overflowY: 'auto' }}
    >
      {isInitialLoading || isLoading ? (
        <div className='w-full'>
          <MasonryGrid columns={columnCount} spacing='md'>
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <TweetCardSkeleton key={`skeleton-${index}`} />
              ))}
          </MasonryGrid>
        </div>
      ) : (
        <AnimatePresence>
          <motion.div
            variants={containerVariants}
            initial='hidden'
            animate='visible'
            className='w-full'
          >
            <MasonryGrid columns={columnCount} spacing='md'>
              {resources.map((resource) => (
                <motion.div
                  key={resource.id}
                  variants={itemVariants}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className='transition-all duration-300 hover:shadow-md'
                >
                  <TweetCard resource={resource} />
                </motion.div>
              ))}
            </MasonryGrid>
          </motion.div>
        </AnimatePresence>
      )}

      {!isInitialLoading && resources.length === 0 && !isLoading && (
        <div className='flex flex-col items-center justify-center py-16 text-center'>
          <div className='bg-muted/30 p-4 rounded-full mb-4'>
            <svg
              className='w-8 h-8 text-muted-foreground'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </div>
          <h3 className='text-xl font-medium mb-2'>No tweets found</h3>
          <p className='text-muted-foreground max-w-md'>
            There are no tweets to display at the moment. Try refreshing or
            check back later.
          </p>
        </div>
      )}
      {loadingMarkup}
    </div>
  );
};
