import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import { GlowingEffect } from '@/components/ui/glowing-effect';

import { useState } from 'react';
import { ExternalLink, Globe } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils';
import { CardActions } from '@/components/CardActions/CardActions';

interface SiteCardProps {
  resource: AppResource;
}

export const SiteCard = ({ resource }: SiteCardProps) => {
  const { tags, hasAdminRole } = useRootContext();
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  const handleImageError = () => {
    setImageError(true);
  };

  const getDomainFromUrl = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  return (
    <div
      className={cn(
        'group  rounded-xl border border-border/50 bg-card/50 backdrop-blur-sm',
        'cursor-pointer',
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        disabled={false}
        spread={40}
        glow={true}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={1.5}
      />

      <div className='relative p-6 space-y-4'>
        {/* Header with title and domain */}
        <div className='space-y-3'>
          <div className='flex items-start justify-between gap-3'>
            <div className='flex-1 min-w-0 space-y-2'>
              <h3 className='text-lg font-semibold leading-tight text-foreground group-hover:text-primary transition-colors duration-200 line-clamp-2'>
                {resource.title}
              </h3>

              <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                <Globe className='h-3.5 w-3.5' />
                <span className='break-all'>
                  {getDomainFromUrl(resource.url)}
                </span>
              </div>
            </div>

            <CardActions
              resource={resource}
              variant='floating'
              showEditDelete={hasAdminRole}
            />
          </div>
        </div>

        {/* Content area with image and description */}
        <div className='flex gap-4'>
          {/* Image thumbnail */}
          <div
            className='relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden bg-muted/50 border border-border/50 cursor-pointer'
            onClick={() => window.open(resource.url, '_blank')}
          >
            {!imageError && resource.imageUrl ? (
              <img
                src={resource.imageUrl}
                alt={resource.title || 'Site preview'}
                className='w-full h-full object-cover transition-transform duration-300 group-hover:scale-105'
                onError={handleImageError}
              />
            ) : (
              <div className='flex items-center justify-center w-full h-full bg-gradient-to-br from-primary/10 to-secondary/10'>
                <Globe className='h-8 w-8 text-muted-foreground/60' />
              </div>
            )}

            {/* Hover overlay */}
            <div
              className={cn(
                'absolute inset-0 bg-black/20 flex items-center justify-center transition-opacity duration-200',
                isHovered ? 'opacity-100' : 'opacity-0',
              )}
            >
              <ExternalLink className='h-4 w-4 text-white' />
            </div>
          </div>

          {/* Description */}
          <div className='flex-1 min-w-0'>
            <p className='text-sm text-muted-foreground leading-relaxed line-clamp-3'>
              {resource.description}
            </p>
          </div>
        </div>

        {/* Tags */}
        {tagsList.length > 0 && (
          <div className='flex flex-wrap gap-1.5'>
            {tagsList.slice(0, 4).map((tag) => (
              <Badge
                key={tag.id}
                variant='secondary'
                className='text-xs px-2 py-0.5 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors'
              >
                {tag.name}
              </Badge>
            ))}
            {tagsList.length > 4 && (
              <Badge variant='outline' className='text-xs px-2 py-0.5'>
                +{tagsList.length - 4}
              </Badge>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
