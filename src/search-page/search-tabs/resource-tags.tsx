import { AppTag } from '@/core.types';
import { SearchTag } from '../search-tags/search-tag';
import { useSearchFiltersContext } from '../search-filters-context';
import { cn } from '@/utils';

export const ResourceTags = ({
  tags,
  className = '',
}: {
  tags: AppTag[];
  className?: string;
}) => {
  const { selectedTagIds } = useSearchFiltersContext();

  return (
    <div className={cn('flex flex-wrap items-center gap-2 mt-auto', className)}>
      {tags.map((tag) => {
        const isTagSelected = selectedTagIds.includes(tag?.id);

        return (
          <SearchTag
            key={tag.id}
            {...{
              tag,
              isTagSelected,
            }}
          />
        );
      })}
    </div>
  );
};
