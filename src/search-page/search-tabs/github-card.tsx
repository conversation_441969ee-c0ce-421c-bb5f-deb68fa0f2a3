import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { cn } from '@/utils';
import { useLocalStorage } from 'usehooks-ts';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import {
  firebaseTimestampToFriendlyDate,
  getUserNameRepoFromUrl,
} from '../../services/utils-service';

import { ResourceTags } from './resource-tags';
import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { Repository } from '../../components/react-github-embed/lib/';

import { CardActions } from '@/components/CardActions/CardActions';

interface GithubCardProps {
  resource: AppResource;
}

export const GithubCard = ({ resource }: GithubCardProps) => {
  const { userName, repoName } = getUserNameRepoFromUrl(resource.url);
  const { authors, tags, hasAdminRole } = useRootContext();

  const [isDarkMode] = useLocalStorage<boolean>('usehooks-ts-dark-mode', false);

  const author = authors.find((author) => author.id === resource.authorId);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  return (
    <Card className='flex flex-col cursor-pointer relative group'>
      <GlowingEffect
        disabled={false}
        spread={40}
        glow={true}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={1.5}
      />
      <CardActions
        resource={resource}
        variant='floating'
        showEditDelete={hasAdminRole}
      />
      <div className='flex justify-end f-full pt-2'>
        <span className='font-semibold whitespace-pre-wrap text-black dark:text-[#d4d1d1] flex gap-2 items-center text-[12px] mr-auto ml-6'>
          {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
        </span>
      </div>
      <div className='py-4 pl-6'>
        <AppAuthorNameAvatar
          author={author}
          nameLength={130}
          customTwitterId={resource.customTwitterId}
        />
      </div>
      <CardContent>
        <div
          className={cn('flex justify-center', {
            dark: isDarkMode,
            light: !isDarkMode,
          })}
        >
          <Repository
            username={userName}
            repository={repoName}
            theme={isDarkMode ? 'dark' : 'light'}
          />
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
    </Card>
  );
};
