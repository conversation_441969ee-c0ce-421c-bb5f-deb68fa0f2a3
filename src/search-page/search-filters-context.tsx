// import { useRootContext } from '@/context/useRootContext';
import { AppResourceType } from '@/core.types';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useSearchParams } from 'react-router-dom';

interface SearchContextProps {
  search: string;
  setSearch: (value: string) => void;
  selectedTagIds: string[];
  setSelectedTagIds: (tags: string[]) => void;
  authorId: string;
  setAuthorID: (authorId: string) => void;
  updateSearch: (match: string) => void;
  resourceType: AppResourceType;
  setResourceType: (resourceType: AppResourceType) => void;
}

export const SearchFiltersContext = createContext<SearchContextProps>(
  undefined as never,
);

export const useSearchFiltersContext = () => {
  const context = useContext(SearchFiltersContext);

  if (!context) {
    throw new Error(
      'useSearchContext must be used within a SearchFiltersContextProvider',
    );
  }

  return context;
};

export const SearchFiltersContextProvider = ({ children }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const tagsParam = searchParams.get('tags');
  const initialTags = tagsParam ? JSON.parse(tagsParam) : [];
  // const { hasAdminRole } = useRootContext();
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>(initialTags);
  const [authorId, setAuthorID] = useState<string>(
    searchParams.get('authorId') ?? '',
  );
  const [resourceType, setResourceType] = useState<AppResourceType>(
    (searchParams.get('resourceType') as AppResourceType) ||
      AppResourceType.Article,
  );

  const [search, setSearch] = useState(searchParams.get('search') ?? '');

  const updateSearch = useCallback(
    (match: string) => {
      if (match === search) return;
      setSearch(match);
    },
    [search],
  );

  // Sync state to URL when it changes
  useEffect(() => {
    const params = new URLSearchParams();

    if (search) params.set('search', search);
    if (selectedTagIds.length)
      params.set('tags', JSON.stringify(selectedTagIds));
    if (authorId) params.set('authorId', authorId);
    if (resourceType) params.set('resourceType', resourceType);

    setSearchParams(params, { replace: true });
  }, [search, selectedTagIds, authorId, setSearchParams, resourceType]);

  // if (!hasAdminRole) return;

  return (
    <SearchFiltersContext.Provider
      value={{
        search,
        setSearch,
        selectedTagIds,
        setSelectedTagIds,
        authorId,
        resourceType,
        setAuthorID,
        updateSearch,
        setResourceType,
      }}
    >
      {children}
    </SearchFiltersContext.Provider>
  );
};
