import { useState, useCallback } from 'react';
import { AppResource } from '../core.types';
import { useBooleanState } from '../hooks/useBooleanState';

export const useEditResourceLogic = () => {
  const [appResourceToEdit, setAppResourceToEdit] =
    useState<AppResource | null>(null);
  const [
    editAppResourceModalOpened,
    {
      setTrue: _openEditAppResourceModal,
      setFalse: _closeEditAppResourceModal,
    },
  ] = useBooleanState(false);
  const closeEditAppResourceModal = useCallback(() => {
    _closeEditAppResourceModal();
    setAppResourceToEdit(null);
  }, [_closeEditAppResourceModal]);

  const openEditAppResourceModal = useCallback(
    (appResource: AppResource) => {
      setAppResourceToEdit(appResource);
      _openEditAppResourceModal();
    },
    [_openEditAppResourceModal],
  );

  return {
    appResourceToEdit,
    editAppResourceModalOpened,
    closeEditAppResourceModal,
    openEditAppResourceModal,
  };
};
