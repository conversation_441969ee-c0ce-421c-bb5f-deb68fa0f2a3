import { deleteAppResource } from '@/api/app-resource.api';
import { AppConfirm } from '@/components/AppConfirm';
import { Button } from '@/components/ui/button';
import { useRootContext } from '@/context/useRootContext';
import { Trash2 } from 'lucide-react';
import { useAppResourcesContext } from '../app-resources-context';

export const DeleteAction = ({ resourceId }: { resourceId: string }) => {
  const { setResources } = useAppResourcesContext();
  const { hasAdminRole } = useRootContext();

  const onDeleteConfirm = async () => {
    await deleteAppResource(resourceId);
    setResources((prev) => prev.filter((r) => r.id !== resourceId));
  };

  if (!hasAdminRole) return null;

  return (
    <AppConfirm
      onConfirm={onDeleteConfirm}
      message='Are you sure to Delete this resource?'
    >
      <Button variant='ghost' size='icon'>
        <Trash2 className='h-4 w-4' />
      </Button>
    </AppConfirm>
  );
};
