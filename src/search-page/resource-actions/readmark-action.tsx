import { <PERSON><PERSON><PERSON><PERSON>, Circle } from 'lucide-react';
import { useCallback, useState } from 'react';
import { addReadMark, removeReadMark } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { AppReadMark } from '../../core.types';
import { Button } from '../../components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '../../components/ui/tooltip';

type ReadMarkActionProps = {
  appResourceId: string;
};

export const ReadMarkAction = ({ appResourceId }: ReadMarkActionProps) => {
  const [loading, setLoading] = useState(false);
  const { readMarks, currentUserId, setReadMarks, isAuthorized } =
    useRootContext();

  const onAddClick = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    const id = (await addReadMark(appResourceId)) as string;
    setReadMarks((prevReadMarks: AppReadMark[]) => [
      ...prevReadMarks,
      { resourceId: appResourceId, userId: currentUserId, id },
    ]);
    setLoading(false);
  }, [appResourceId, currentUserId, loading, setReadMarks]);

  const onRemoveClick = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    await removeReadMark(appResourceId);

    setReadMarks((prevReadMarks: AppReadMark[]) =>
      prevReadMarks.filter(
        (readmark) =>
          readmark.resourceId !== appResourceId ||
          readmark.userId !== currentUserId,
      ),
    );
    setLoading(false);
  }, [appResourceId, currentUserId, loading, setReadMarks]);

  const isReadMarked = readMarks.some(
    (readmark) =>
      readmark.resourceId === appResourceId &&
      currentUserId === readmark.userId,
  );

  const label = isReadMarked ? 'Mark as unread' : 'Mark as read';

  if (!isAuthorized) return null;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          style={{
            cursor: 'pointer',
            borderRadius: '50%',
          }}
          onClick={isReadMarked ? onRemoveClick : onAddClick}
        >
          {isReadMarked ? (
            <CheckCircle className='h-4 w-4 transform transition-transform duration-150 ease-in-out active:scale-150' />
          ) : (
            <Circle className='h-4 w-4 transform transition-transform duration-150 ease-in-out active:scale-150' />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{label}</TooltipContent>
    </Tooltip>
  );
};
