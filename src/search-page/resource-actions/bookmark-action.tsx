import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Bookmark } from 'lucide-react';
import { useCallback, useState } from 'react';
import { addBookmark, removeBookmark } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { AppBookmark } from '../../core.types';
import { useAppResourcesContext } from '../app-resources-context';

type BookmarkActionProps = {
  appResourceId: string;
};

export const BookmarkAction = ({ appResourceId }: BookmarkActionProps) => {
  const [loading, setLoading] = useState(false);
  const { bookmarks, currentUserId, setBookmarks, isAuthorized } =
    useRootContext();
  const { setResources, bookmarksMode } = useAppResourcesContext();

  const onAddClick = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    const id = (await addBookmark(appResourceId)) as string;
    setBookmarks((prevBookmarks: AppBookmark[]) => [
      ...prevBookmarks,
      { resourceId: appResourceId, userId: currentUserId, id },
    ]);
    setLoading(false);
  }, [appResourceId, currentUserId, loading, setBookmarks]);

  const onRemoveClick = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    await removeBookmark(appResourceId);

    setBookmarks((prevBookmarks: AppBookmark[]) =>
      prevBookmarks.filter(
        (bookmark) =>
          bookmark.resourceId !== appResourceId ||
          bookmark.userId !== currentUserId,
      ),
    );

    if (bookmarksMode) {
      setResources((resources) =>
        resources.filter((appResource) => appResource.id !== appResourceId),
      );
    }

    setLoading(false);
  }, [
    appResourceId,
    bookmarksMode,
    currentUserId,
    loading,
    setBookmarks,
    setResources,
  ]);

  const isBookmarked = bookmarks.some(
    (bookmark) =>
      bookmark.resourceId === appResourceId &&
      currentUserId === bookmark.userId,
  );

  const label = isBookmarked ? 'Remove bookmark' : 'Add bookmark';

  if (!isAuthorized) return null;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          style={{
            cursor: 'pointer',
            borderRadius: '50%',
          }}
          disabled={loading}
          onClick={isBookmarked ? onRemoveClick : onAddClick}
        >
          {isBookmarked ? (
            <Bookmark className='h-4 w-4 fill-current text-yellow-500 transform transition-transform duration-150 ease-in-out active:scale-150' />
          ) : (
            <Bookmark className='h-4 w-4 transform transition-transform duration-150 ease-in-out active:scale-150' />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{label}</TooltipContent>
    </Tooltip>
  );
};
