import { getRatingRecord, rateAppResource } from '@/api';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { AppResource, RateType } from '@/core.types';
import { ArrowDown } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { useAppResourcesContext } from '../app-resources-context';

export const DislikeAction = ({ resource }: { resource: AppResource }) => {
  const [loading, setLoading] = useState(false);
  const { onRateResource, resetLikesDislikes } = useAppResourcesContext();

  const onDislikeClick = useCallback(async () => {
    if (loading) return;
    const ratingRecord = await getRatingRecord(resource.id);

    const initialLikes = resource.likesCount;
    const initialDislikes = resource.dislikesCount;

    try {
      setLoading(true);
      if (ratingRecord?.type === RateType.Dislike) {
        toast.error('You already disliked this resource');
        setLoading(false);
        return;
      }

      onRateResource(
        resource.id,
        RateType.Dislike,
        ratingRecord?.type === RateType.Like,
      );
      await rateAppResource({
        resourceId: resource.id,
        type: RateType.Dislike,
      });
      setLoading(false);
    } catch (error) {
      console.log(error);
      resetLikesDislikes(resource.id, initialLikes, initialDislikes);
    }
  }, [
    loading,
    resource.id,
    resource.likesCount,
    resource.dislikesCount,
    onRateResource,
    resetLikesDislikes,
  ]);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant='ghost'
          onClick={onDislikeClick}
          size='icon'
          style={{
            cursor: 'pointer',
            borderRadius: '50%',
          }}
        >
          <ArrowDown className='h-4 w-4 text-blue-500 transform transition-transform duration-150 ease-in-out active:scale-150' />
        </Button>
      </TooltipTrigger>
      <TooltipContent>{resource.dislikesCount} Downvotes</TooltipContent>
    </Tooltip>
  );
};
