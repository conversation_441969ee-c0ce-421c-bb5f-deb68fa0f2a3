import { AppResource, RateType } from '@/core.types';
import { useLazyLoading } from '@/hooks/useLazyLoading';
import {
  useCallback,
  useState,
  useContext,
  createContext,
  useEffect,
} from 'react';
import { getAppResourceList, getBookmarkedAppResources } from '../api';
import { useSearchFiltersContext } from './search-filters-context';

import { useRootContext } from '@/context/useRootContext';
import { LoadingTrigger } from '@/components/LoadingTrigger';

const LIMIT = 15;

interface AppResourcesContextType {
  resetAndReloadResources: () => void;
  loading: boolean;
  loadingMarkup: JSX.Element | null;
  resources: AppResource[];
  setResources: React.Dispatch<React.SetStateAction<AppResource[]>>;
  bookmarksMode: boolean;
  showRead: boolean;
  setShowRead: (showRead: boolean) => void;
  showBookMarks: () => void;
  hideBookMarks: () => void;
  onRateResource: (
    appResourceId: string,
    type: RateType,
    decrementOtherRate: boolean,
  ) => void;
  resetLikesDislikes: (
    appResourceId: string,
    likesCount: number,
    dislikesCount: number,
  ) => void;
}

const AppResourcesContext = createContext<AppResourcesContextType | undefined>(
  undefined,
);

export const AppResourcesProvider = ({ children }) => {
  const [resources, setResources] = useState<AppResource[]>([]);
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const { search, selectedTagIds, authorId, resourceType } =
    useSearchFiltersContext();

  const { bookmarks, readMarks } = useRootContext();

  const [bookmarksMode, setBookmarksMode] = useState(false);
  const [showRead, setShowRead] = useState(false);

  const getResources = useCallback(async () => {
    const cursor = !resources.length
      ? null
      : resources[resources.length - 1].id;

    const newAppResources = await getAppResourceList({
      search,
      cursor,
      authorId,
      resourceType,
      searchLimit: LIMIT,
      tags: selectedTagIds,
    });

    if (newAppResources.length < LIMIT) {
      setHasMoreItems(false);
    }

    setResources((appResources) => [...appResources, ...newAppResources]);
  }, [search, selectedTagIds, authorId, resourceType, resources]);

  const getResourcesFromBookmarks = useCallback(async () => {
    const { appResources } = await getBookmarkedAppResources(bookmarks);
    setResources(appResources);
  }, [bookmarks]);

  const [initialLoading, setInitialLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  const loadMoreResources = useCallback(async () => {
    if (bookmarksMode || !hasMoreItems || loadingMore || initialLoading) return;

    setLoadingMore(true);
    try {
      await getResources();
    } finally {
      setLoadingMore(false);
    }
  }, [bookmarksMode, getResources, hasMoreItems, initialLoading, loadingMore]);

  const { loadMoreRef } = useLazyLoading(loadMoreResources, {
    hasMore: hasMoreItems,
    isLoading: loadingMore,
    threshold: 0.1,
    rootMargin: '100px',
  });

  // Initial loading effect
  useEffect(() => {
    if (initialLoading) {
      const loadInitialData = async () => {
        try {
          if (bookmarksMode) {
            await getResourcesFromBookmarks();
          } else {
            await getResources();
          }
        } finally {
          setInitialLoading(false);
        }
      };
      loadInitialData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialLoading, bookmarksMode]);

  const loadingMarkup = (
    <LoadingTrigger
      isLoading={initialLoading || loadingMore}
      hasMore={hasMoreItems}
      loadMoreRef={loadMoreRef}
    />
  );

  const resetAndReloadResources = useCallback(() => {
    setResources([]);
    setHasMoreItems(true);
    setInitialLoading(true);
  }, [setResources, setInitialLoading]);

  const showBookMarks = useCallback(() => {
    setResources([]);
    setBookmarksMode(true);
    setHasMoreItems(false);
    setInitialLoading(true);
  }, [setBookmarksMode, setHasMoreItems, setInitialLoading]);

  const hideBookMarks = useCallback(() => {
    setBookmarksMode(false);
    resetAndReloadResources();
  }, [resetAndReloadResources]);

  const onRateResource = useCallback(
    (appResourceId: string, type: RateType, decrementOtherRate: boolean) => {
      setResources((prevAppResources) =>
        prevAppResources.map((appResource) => {
          const key = type === RateType.Like ? 'likesCount' : 'dislikesCount';
          const otherKey =
            type === RateType.Like ? 'dislikesCount' : 'likesCount';

          if (appResource.id === appResourceId) {
            return {
              ...appResource,
              [key]: (appResource[key] || 0) + 1,
              [otherKey]: decrementOtherRate
                ? (appResource[otherKey] || 0) - 1
                : appResource[otherKey],
            };
          }
          return appResource;
        }),
      );
    },
    [setResources],
  );

  const resetLikesDislikes = useCallback(
    (appResourceId: string, likesCount: number, dislikesCount: number) => {
      setResources((prevAppResources) =>
        prevAppResources.map((appResources) => {
          if (appResources.id === appResourceId) {
            return {
              ...appResources,
              likesCount,
              dislikesCount,
            };
          }
          return appResources;
        }),
      );
    },
    [setResources],
  );

  const finalResources = bookmarksMode
    ? resources.filter((x) => x.type === resourceType)
    : showRead
    ? resources.filter((x) => readMarks.some((y) => y.resourceId === x.id))
    : resources;

  return (
    <AppResourcesContext.Provider
      value={{
        onRateResource,
        resetLikesDislikes,
        bookmarksMode,
        showRead,
        setShowRead,
        showBookMarks,
        hideBookMarks,
        resetAndReloadResources,
        loading: initialLoading || loadingMore,
        loadingMarkup,
        resources: finalResources,
        setResources,
      }}
    >
      {children}
    </AppResourcesContext.Provider>
  );
};

export const useAppResourcesContext = (): AppResourcesContextType => {
  const context = useContext(AppResourcesContext);
  if (context === undefined) {
    throw new Error(
      'useAppResourcesContext must be used within an AppResourcesProvider',
    );
  }
  return context;
};
