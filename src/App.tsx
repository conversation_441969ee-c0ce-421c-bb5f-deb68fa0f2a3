import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { Toaster } from 'sonner';
import { HelmetProvider } from 'react-helmet-async';
import { AdminPage } from './admin-page/AdminPage';
import { AdminTagsPage } from './admin-page/AdminTagsPage';
import { AdminAuthorsPage } from './admin-page/AdminAuthorsPage';
import { AdminResourcesPage } from './admin-page/AdminResourcesPage';
import { AdminProposalsPage } from './admin-page/AdminProposalsPage';
import { AdminBulkImportPage } from './admin-page/AdminBulkImportPage';
import { RootProvider } from './context/RootContext';
import { AdminCountersProvider } from './context/AdminCountersContext';
import ErrorPage from './error-page/ErrorPage';
import { useEffect, useState } from 'react';
import { useDarkMode } from 'usehooks-ts';
import { SearchFiltersContextProvider } from './search-page/search-filters-context';
import { AppResourcesProvider } from './search-page/app-resources-context';
import { SearchPage } from './search-page/search-page';

// Wrapper component for search page with necessary providers
const SearchWrapper = ({ children }: { children: React.ReactNode }) => (
  <SearchFiltersContextProvider>
    <AppResourcesProvider>
      <div className='h-screen flex flex-col'>{children}</div>
    </AppResourcesProvider>
  </SearchFiltersContextProvider>
);

// Wrapper component for admin routes with all necessary providers
const AdminWrapper = ({ children }: { children: React.ReactNode }) => (
  <SearchFiltersContextProvider>
    <AppResourcesProvider>
      <AdminCountersProvider>{children}</AdminCountersProvider>
    </AppResourcesProvider>
  </SearchFiltersContextProvider>
);

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <SearchWrapper>
        <SearchPage />
      </SearchWrapper>
    ),
  },
  {
    path: '/admin',
    element: (
      <AdminWrapper>
        <AdminPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/tags',
    element: (
      <AdminWrapper>
        <AdminTagsPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/authors',
    element: (
      <AdminWrapper>
        <AdminAuthorsPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/resources',
    element: (
      <AdminWrapper>
        <AdminResourcesPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/proposals',
    element: (
      <AdminWrapper>
        <AdminProposalsPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/bulk-import',
    element: (
      <AdminWrapper>
        <AdminBulkImportPage />
      </AdminWrapper>
    ),
    errorElement: <ErrorPage />,
  },
]);

function App() {
  const { isDarkMode } = useDarkMode(true);
  const [restricted, setRestricted] = useState(false);

  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then((response) => response.json())
      .then((data) => {
        console.log(data);
        if (['RU', 'IR', 'KP', 'CN'].includes(data.country)) {
          setRestricted(true);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('bodyDark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('bodyDark');
    }
  }, [isDarkMode]);

  if (restricted) {
    return (
      <div className='fixed inset-0 flex justify-center items-center'>
        russia is a terrorist state
      </div>
    );
  }

  return (
    <HelmetProvider>
      <RootProvider>
        <RouterProvider router={router} />
        <Toaster />
      </RootProvider>
    </HelmetProvider>
  );
}

export default App;
