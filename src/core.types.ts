import { Timestamp } from 'firebase/firestore';
import { AppSelectOption } from './components/AppSelect/AppSelect';

export enum TagGroup {
  HotTopics = 'HotTopics',
  ERC = 'ERC',
  EIP = 'EIP',
  DeveloperInfrastructure = 'DeveloperInfrastructure',
  CommonAttacks = 'CommonAttacks',
  DeFiAttacks = 'DeFiAttacks',
  SpecificAttackVectors = 'SpecificAttackVectors',
  BlockchainConceptsAndTechniques = 'BlockchainConceptsAndTechniques',
  DeFi = 'DeFi',
  CrossChain = 'CrossChain',
  CareerPath = 'CareerPath',
  Tests = 'Tests',
  Challenges = 'Challenges',
  Rest = 'Rest',
}

export const TagGroupName = {
  [TagGroup.HotTopics]: '🔥 Hot Topics',
  [TagGroup.ERC]: '📄 ERC',
  [TagGroup.EIP]: '📜 EIP',
  [TagGroup.DeveloperInfrastructure]: '🔨 Developer Infrastructure',
  [TagGroup.BlockchainConceptsAndTechniques]:
    '🧠 Blockchain Concepts And Techniques',
  [TagGroup.CrossChain]: '🌉 Cross Chain',
  [TagGroup.DeFi]: '💰 DeFi',
  [TagGroup.CommonAttacks]: '🛡️ Common Attacks',
  [TagGroup.SpecificAttackVectors]: '⚔️ Specific Attack Vectors',
  [TagGroup.DeFiAttacks]: '💹 DeFi Attacks',
  [TagGroup.Tests]: '🧪 Tests',
  [TagGroup.Challenges]: '🏅 Challenges',
  [TagGroup.CareerPath]: '🚀 Career Path',
  [TagGroup.Rest]: '☕ Rest',
};

export const TagGroupSelectOptions: AppSelectOption[] = [
  {
    value: TagGroup.HotTopics,
    label: TagGroupName[TagGroup.HotTopics],
  },
  {
    value: TagGroup.ERC,
    label: TagGroupName[TagGroup.ERC],
  },
  {
    value: TagGroup.EIP,
    label: TagGroupName[TagGroup.EIP],
  },
  {
    value: TagGroup.DeveloperInfrastructure,
    label: TagGroupName[TagGroup.DeveloperInfrastructure],
  },
  {
    value: TagGroup.BlockchainConceptsAndTechniques,
    label: TagGroupName[TagGroup.BlockchainConceptsAndTechniques],
  },
  {
    value: TagGroup.DeFi,
    label: TagGroupName[TagGroup.DeFi],
  },
  {
    value: TagGroup.CommonAttacks,
    label: TagGroupName[TagGroup.CommonAttacks],
  },
  {
    value: TagGroup.DeFiAttacks,
    label: TagGroupName[TagGroup.DeFiAttacks],
  },
  {
    value: TagGroup.SpecificAttackVectors,
    label: TagGroupName[TagGroup.SpecificAttackVectors],
  },
  {
    value: TagGroup.CrossChain,
    label: TagGroupName[TagGroup.CrossChain],
  },
  {
    value: TagGroup.CareerPath,
    label: TagGroupName[TagGroup.CareerPath],
  },
  {
    value: TagGroup.Tests,
    label: TagGroupName[TagGroup.Tests],
  },
  {
    value: TagGroup.Challenges,
    label: TagGroupName[TagGroup.Challenges],
  },
  {
    value: TagGroup.Rest,
    label: TagGroupName[TagGroup.Rest],
  },
];

export type AppTag = {
  id: string;
  group: TagGroup;
  name: string;
  parentTagId?: string;
};

export type AppAuthor = {
  id: string;
  name: string;
  twitter: string;
  avatarUrl: string;
};

export enum AppResourceType {
  Article = 'article',
  Video = 'video',
  Tweet = 'tweet',
  Github = 'github',
  Papers = 'papers',
  Sites = 'sites',
}

export const ResourceTypeOptions = [
  {
    value: AppResourceType.Article,
    label: 'Article',
  },
  {
    value: AppResourceType.Video,
    label: 'Video',
  },
  {
    value: AppResourceType.Tweet,
    label: 'Tweet',
  },
  {
    value: AppResourceType.Github,
    label: 'Github',
  },
  {
    value: AppResourceType.Papers,
    label: 'Papers',
  },
  {
    value: AppResourceType.Sites,
    label: 'Site',
  },
];

export type AppResource = {
  id: string;
  title: string;
  url: string;
  tags: string[];
  likesCount: number;
  dislikesCount: number;
  authorId: string;
  createdAt: string | Timestamp;
  type: AppResourceType;
  customTwitterId?: string;
  assessmentId?: string;
  imageUrl?: string;
  description?: string;
};

export enum AppProposalStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

export enum RateType {
  Like = 'like',
  Dislike = 'dislike',
}

export type RatingItem = {
  id: string;
  userId: string;
  resourceId: string;
  type: RateType;
};

export type AppBookmark = {
  id: string;
  userId: string;
  resourceId: string;
};

export type AppReadMark = {
  id: string;
  userId: string;
  resourceId: string;
};

export enum UpdateType {
  Author = 'author',
  Tag = 'tag',
}

export type AppProposal = {
  id: string;
  title: string;
  proposerId: string;
  authorId?: string;
  customTwitterId?: string;
  url: string;
  comment: string;
  status: AppProposalStatus;
  type: AppResourceType;
  createdAt: string;
  tags: string[];
  rejectReason?: string;
};
