import { useState, useCallback } from 'react';
import type { ViewMode } from '@/components/ViewSwitcher';

export type { ViewMode };

export function useTableView(defaultView: ViewMode = 'grid') {
  const [viewMode, setViewMode] = useState<ViewMode>(defaultView);

  const toggleView = useCallback(() => {
    setViewMode((current) => (current === 'table' ? 'grid' : 'table'));
  }, []);

  const setTableView = useCallback(() => {
    setViewMode('table');
  }, []);

  const setGridView = useCallback(() => {
    setViewMode('grid');
  }, []);

  return {
    viewMode,
    setViewMode,
    toggleView,
    setTableView,
    setGridView,
    isTableView: viewMode === 'table',
    isGridView: viewMode === 'grid',
  };
}
