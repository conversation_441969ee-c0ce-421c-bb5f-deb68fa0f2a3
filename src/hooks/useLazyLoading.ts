import { useCallback, useEffect, useState } from 'react';
import { useIntersectionObserver } from 'usehooks-ts';

interface UseLazyLoadingOptions {
  hasMore: boolean;
  isLoading: boolean;
  threshold?: number;
  rootMargin?: string;
}

interface UseLazyLoadingReturn {
  loadMoreRef: (node?: Element | null) => void;
  isIntersecting: boolean;
  triggerLoadMore: () => void;
}

export function useLazyLoading(
  loadMore: () => Promise<void> | void,
  options: UseLazyLoadingOptions
): UseLazyLoadingReturn {
  const { hasMore, isLoading, threshold = 0.1, rootMargin = '100px' } = options;
  const [shouldLoad, setShouldLoad] = useState(false);

  const { isIntersecting, ref } = useIntersectionObserver({
    threshold,
    rootMargin,
    freezeOnceVisible: false,
  });

  const triggerLoadMore = useCallback(async () => {
    if (hasMore && !isLoading) {
      setShouldLoad(true);
      try {
        await loadMore();
      } finally {
        setShouldLoad(false);
      }
    }
  }, [hasMore, isLoading, loadMore]);

  // Trigger load more when intersection is detected
  useEffect(() => {
    if (isIntersecting && hasMore && !isLoading && !shouldLoad) {
      triggerLoadMore();
    }
  }, [isIntersecting, hasMore, isLoading, shouldLoad, triggerLoadMore]);

  return {
    loadMoreRef: ref,
    isIntersecting,
    triggerLoadMore,
  };
}
