import { useCallback, useEffect, useState } from 'react';

import { Spinner } from '@/components/spinner';
import { useIntersectionObserver } from './use-intersection-observer';

type PaginationRequestDTO = {
  initialRequest: () => Promise<void>;
  fetchRequest: () => Promise<void>;
  hasMoreItems: boolean;
};

export const usePaginationRequest = ({
  initialRequest,
  fetchRequest,
  hasMoreItems,
}: PaginationRequestDTO) => {
  const [loadingMore, setLoadingMore] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold: 0.5,
  });

  const loadMoreItems = useCallback(async () => {
    if (hasMoreItems && !loadingMore) {
      setLoadingMore(true);
      await fetchRequest();
      setLoadingMore(false);
    }
  }, [fetchRequest, hasMoreItems, loadingMore]);

  useEffect(() => {
    if (isIntersecting && !initialLoading && hasMoreItems) {
      loadMoreItems();
    }
  }, [isIntersecting, initialLoading, loadMoreItems, hasMoreItems]);

  useEffect(() => {
    if (initialLoading && !loadingMore) {
      setLoadingMore(true);
      initialRequest().then(() => {
        setInitialLoading(false);
        setLoadingMore(false);
      });
    }
  }, [initialLoading, initialRequest, loadingMore]);

  const loadingMarkup = (
    <div
      ref={ref}
      className='flex h-[30px] items-center justify-center p-4 w-full'
    >
      {(initialLoading || loadingMore) && <Spinner />}
    </div>
  );

  // TODO: add empty state component

  return {
    loadingMarkup,
    setInitialLoading,
    initialLoading,
    loadingMore,
    hasMoreItems,
    ref,
  };
};
