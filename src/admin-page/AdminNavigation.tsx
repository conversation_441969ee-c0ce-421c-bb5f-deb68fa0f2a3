import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAdminCounters } from '../context/AdminCountersContext';

const adminRoutes = [
  {
    path: '/admin/tags',
    label: 'Tags',
    counterKey: 'tags' as const,
  },
  {
    path: '/admin/authors',
    label: 'Authors',
    counterKey: 'authors' as const,
  },
  {
    path: '/admin/resources',
    label: 'App resources',
    counterKey: 'resources' as const,
  },
  {
    path: '/admin/proposals',
    label: 'Proposals',
  },
  {
    path: '/admin/bulk-import',
    label: 'Bulk Import',
  },
];

export const AdminNavigation = () => {
  const location = useLocation();
  const { counters, loading } = useAdminCounters();

  return (
    <nav className='flex flex-wrap gap-2 p-4 border-b border-gray-200 dark:border-gray-700'>
      {adminRoutes.map((route) => {
        const count = route.counterKey ? counters[route.counterKey] : undefined;

        return (
          <Link
            key={route.path}
            to={route.path}
            className={cn(
              'px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2',
              'hover:bg-gray-100 dark:hover:bg-gray-800',
              location.pathname === route.path
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-600 dark:text-gray-300',
            )}
          >
            <span>{route.label}</span>
            {route.counterKey && (
              <span
                className={cn(
                  'px-2 py-1 text-xs rounded-full',
                  location.pathname === route.path
                    ? 'bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200'
                    : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300',
                )}
              >
                {loading ? '...' : count}
              </span>
            )}
          </Link>
        );
      })}
    </nav>
  );
};
