import { SubmitHandler } from 'react-hook-form';

import { createAppResource } from '../../api';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppResource } from '../../core.types';
import { ManageAppResourceForm } from './ManageAppResourceForm';

export const ManageAppResources = () => {
  const { refreshCounters } = useAdminCounters();

  const onSubmit: SubmitHandler<AppResource> = async (values) => {
    const resourceId = await createAppResource(values);
    if (resourceId) {
      // Refresh counters after successful creation
      refreshCounters();
    }
  };

  return (
    <div className='flex gap-2 w-[750px] mx-auto mt-4'>
      <ManageAppResourceForm onSubmit={onSubmit} />
    </div>
  );
};
