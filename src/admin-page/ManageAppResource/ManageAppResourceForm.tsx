import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useMemo } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';

import { AppSelect } from '../../components/AppSelect/AppSelect';
import { useRootContext } from '../../context/useRootContext';
import {
  AppResource,
  AppResourceType,
  ResourceTypeOptions,
} from '../../core.types';

import * as yup from 'yup';
import { AppInput } from '../../components/app-input/app-input';
import { AppRadioGroup } from '../../components/app-radio-group/app-radio-group';
import { AppAuthorSelect } from '../../components/AppSelect/AppAuthorSelect';
import { AppSubmitButton } from '../../components/buttons/AppSubmitButton';
import { AppTextarea } from '../../components/app-input/app-textarea';

const appResourceSchema = yup.object({
  url: yup.string().url('Invalid URL'),
  imageUrl: yup.string().url('Invalid URL'),
  createdAt: yup.date(),
});

type ManageAppResourceFormProps = {
  onSubmit: SubmitHandler<AppResource>;
  defaultValues?: AppResource;
};

export const ManageAppResourceForm = ({
  onSubmit,
  defaultValues,
}: ManageAppResourceFormProps) => {
  const {
    register,
    setValue,
    watch,
    handleSubmit,
    formState: { errors: errorsAppResources, isSubmitting },
  } = useForm<AppResource & { tags: string[] }>({
    resolver: yupResolver(appResourceSchema as any),
    defaultValues: defaultValues || {
      createdAt: new Date().toISOString().slice(0, 10),
    },
  });

  const { tags } = useRootContext();

  const tagSelectOptions = useMemo(
    () => tags.map(({ id, name }) => ({ value: id, label: name })),
    [tags],
  );

  const authorId = watch('authorId');
  const selectedTags = watch('tags');
  const typeValue = watch('type');

  const onAuthorChange = (authorId: string) => {
    setValue('authorId', authorId);
  };

  const onTagsChange = (tags: string[]) => {
    setValue('tags', tags);
  };

  const onChangeType = (value: string) => {
    setValue('type', value as AppResourceType);
  };

  useEffect(() => {
    register('authorId');
    register('tags');
    register('type');
    if (defaultValues?.createdAt) {
      setValue('createdAt', (defaultValues.createdAt as string).slice(0, 10));
    }
  }, [defaultValues?.createdAt, register, setValue]);

  return (
    <FormProvider {...({ register } as any)}>
      <form
        onSubmit={handleSubmit(async (...rest) => {
          await onSubmit(...rest);
          setValue('tags', []);
          // @ts-expect-error note
          setValue('authorId', null);
          setValue('url', '');
          setValue('imageUrl', '');
          setValue('description', '');
          setValue('customTwitterId', '');
          setValue('title', '');
        })}
        className='mx-auto w-[100%] flex flex-col gap-[10px]'
      >
        <AppAuthorSelect value={authorId} onChange={onAuthorChange} />
        <AppInput
          type='text'
          name='customTwitterId'
          placeholder='Or write author @twitterId'
        />
        <AppInput
          placeholder='Title'
          name='title'
          type='text'
          error={errorsAppResources.title?.message}
        />
        <AppInput
          placeholder='URL'
          name='url'
          type='text'
          error={errorsAppResources.url?.message}
        />
        <AppInput
          placeholder='Image URL'
          name='imageUrl'
          type='text'
          error={errorsAppResources.imageUrl?.message}
        />
        <AppRadioGroup
          label='Resource type:'
          value={typeValue}
          onChange={onChangeType}
          options={ResourceTypeOptions as any[]}
        />
        <AppSelect
          placeholder='Select Tags'
          value={selectedTags}
          options={tagSelectOptions}
          onChange={onTagsChange}
          multiple
        />
        <AppInput
          placeholder='Created At'
          name='createdAt'
          type='date'
          error={errorsAppResources.createdAt?.message}
        />
        <AppTextarea name='description' placeholder='Description' />
        <AppSubmitButton isSubmitting={isSubmitting} />
      </form>
    </FormProvider>
  );
};
