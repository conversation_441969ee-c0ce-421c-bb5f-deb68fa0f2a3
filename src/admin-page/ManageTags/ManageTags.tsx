import { groupTags } from '@/services/utils-service';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { createTag } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppTag, TagGroupName } from '../../core.types';
import { ManageTagForm } from './ManageTagForm';
import { ManageTagsItem } from './ManageTagsItem';

export const ManageTags = () => {
  const { tags, setTags } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onTagCreate = useCallback(
    async ({ group, name }: { group: any; name: string }) => {
      const isAlreadyAdded = tags.find((tag) => tag.name === name);
      if (isAlreadyAdded) {
        toast.error('Tag is already added');
        return;
      }

      const tagId = await createTag({ group, name } as any);
      if (tagId) {
        setTags((prevTags: AppTag[]) => [
          ...prevTags,
          { group, name, id: tagId },
        ]);
        // Refresh counters after successful creation
        refreshCounters();
      }
    },
    [setTags, tags, refreshCounters],
  );

  const groupedTags = groupTags(tags);

  return (
    <div className='flex gap-3 m-4'>
      <div className='w-[27%]'>
        <ManageTagForm onSubmit={onTagCreate} />
      </div>
      <div className='flex flex-wrap'>
        {Object.entries(groupedTags).map(([group, tagsInGroup]) => (
          <div key={group} className='p-4 m-2 bg-[#e3e4ea] rounded'>
            <h3 className='font-bold text-lg text-black'>
              {TagGroupName[group]}
            </h3>
            <div className='mt-2 gap-1'>
              {(tagsInGroup as AppTag[]).map((tag) => (
                <ManageTagsItem key={tag.id} tag={tag} />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
