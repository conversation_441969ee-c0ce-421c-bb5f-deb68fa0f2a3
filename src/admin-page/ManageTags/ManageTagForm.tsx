import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AppInput } from '../../components/app-input/app-input';
import { AppSelect } from '../../components/AppSelect/AppSelect';
import { AppSubmitButton } from '../../components/buttons/AppSubmitButton';
import { TagGroup, TagGroupSelectOptions } from '../../core.types';

const tagSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  group: z.enum(
    [
      TagGroup.HotTopics,
      TagGroup.ERC,
      TagGroup.EIP,
      TagGroup.DeveloperInfrastructure,
      TagGroup.CommonAttacks,
      TagGroup.DeFiAttacks,
      TagGroup.SpecificAttackVectors,
      TagGroup.BlockchainConceptsAndTechniques,
      TagGroup.DeFi,
      TagGroup.CrossChain,
      TagGroup.CareerPath,
      TagGroup.Tests,
      TagGroup.Challenges,
      TagGroup.Rest,
    ],
    { message: 'Group is required' },
  ),
});

type TagFormData = z.infer<typeof tagSchema>;

export const ManageTagForm = ({
  onSubmit,
  defaultValues,
}: {
  onSubmit: SubmitHandler<TagFormData>;
  defaultValues?: Partial<TagFormData>;
}) => {
  const methods = useForm<TagFormData>({
    resolver: zodResolver(tagSchema),
    defaultValues,
  });

  const {
    register,
    handleSubmit,
    formState: { errors: errorsTag, isSubmitting },
    setValue,
    reset,
    watch,
  } = methods;

  const groupValue = watch('group');

  useEffect(() => {
    register('group');
  }, [register]);

  const onGroupChange = (value: string) => {
    setValue('group', value as TagGroup);
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data) => {
          onSubmit(data);
          setValue('group', '' as TagGroup);
          reset();
        })}
        className='flex flex-col gap-2  mx-auto'
      >
        <AppInput
          name='name'
          placeholder='Name'
          type='text'
          error={errorsTag?.name?.message}
        />
        <AppSelect
          placeholder='Select Group'
          value={groupValue}
          options={TagGroupSelectOptions}
          onChange={onGroupChange}
        />
        <AppSubmitButton isSubmitting={isSubmitting} />
      </form>
    </FormProvider>
  );
};
