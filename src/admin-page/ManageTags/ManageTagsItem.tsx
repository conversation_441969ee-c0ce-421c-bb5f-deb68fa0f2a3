import { deleteTag, updateTag } from '@/api/tags.api';
import { AppConfirm } from '@/components/AppConfirm';
import { useRootContext } from '@/context/useRootContext';
import { useAdminCounters } from '@/context/AdminCountersContext';
import { AppTag } from '../../core.types';
import { EditTagModal } from './EditTagModal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';

type ManageTagsItemProps = {
  tag: AppTag;
};

export const ManageTagsItem = ({ tag }: ManageTagsItemProps) => {
  const { setTags } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onTagEdit = async (values: AppTag) => {
    await updateTag({ ...values, id: values.id } as any);
    setTags((prevTags: AppTag[]) =>
      prevTags.map((tag) => {
        if (tag.id === values.id) {
          return { ...values };
        }
        return tag;
      }),
    );
  };

  const onDeleteTagConfirmed = async () => {
    if (tag.id === null) return;
    await deleteTag(tag.id);
    setTags((prevTags: AppTag[]) => prevTags.filter((x) => x.id !== tag.id));
    // Refresh counters after successful deletion
    refreshCounters();
  };

  return (
    <div className='group'>
      <Badge
        variant='secondary'
        className='flex items-center justify-between w-full p-2 h-auto transition-colors'
      >
        <span className='text-sm font-medium truncate flex-1'>{tag.name}</span>
        <div className='flex items-center gap-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity'>
          <EditTagModal defaultValues={tag} onSubmit={onTagEdit}>
            <Button variant='ghost' size='sm' className='h-5 w-5 p-0 '>
              <Edit className='w-3 h-3' />
            </Button>
          </EditTagModal>

          <AppConfirm
            onConfirm={onDeleteTagConfirmed}
            message='Are you sure you want to delete this tag?'
          >
            <Button variant='ghost' size='sm' className='h-5 w-5 p-0 '>
              <Trash2 className='w-3 h-3' />
            </Button>
          </AppConfirm>
        </div>
      </Badge>
    </div>
  );
};
