import { deleteTag, updateTag } from '@/api/tags.api';
import { AppConfirm } from '@/components/AppConfirm';
import { useRootContext } from '@/context/useRootContext';
import { useAdminCounters } from '@/context/AdminCountersContext';
import { AppTag } from '../../core.types';
import { EditTagModal } from './EditTagModal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';

type ManageTagsItemProps = {
  tag: AppTag;
};

export const ManageTagsItem = ({ tag }: ManageTagsItemProps) => {
  const { setTags } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onTagEdit = async (values: AppTag) => {
    await updateTag({ ...values, id: values.id } as any);
    setTags((prevTags: AppTag[]) =>
      prevTags.map((tag) => {
        if (tag.id === values.id) {
          return { ...values };
        }
        return tag;
      }),
    );
  };

  const onDeleteTagConfirmed = async () => {
    if (tag.id === null) return;
    await deleteTag(tag.id);
    setTags((prevTags: AppTag[]) => prevTags.filter((x) => x.id !== tag.id));
    // Refresh counters after successful deletion
    refreshCounters();
  };

  return (
    <div className='flex items-center justify-between p-4 rounded-md shadow text-black'>
      <span className='text-lg font-medium'>{tag.name}</span>
      <div>
        <EditTagModal defaultValues={tag} onSubmit={onTagEdit}>
          <button className='text-blue-500 hover:text-blue-700 mr-2'>
            Edit
          </button>
        </EditTagModal>

        <AppConfirm
          onConfirm={onDeleteTagConfirmed}
          message='Are you sure to Delete this tag?'
        >
          <button className='text-red-500 hover:text-red-700'>Delete</button>
        </AppConfirm>
      </div>
    </div>
  );
};
