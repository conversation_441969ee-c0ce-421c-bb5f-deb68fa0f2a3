import {
  Table,
  TableBody,
  TableCaption,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useEffect, useState } from 'react';
import { getProposals } from '../../api';
import { AppProposal } from '../../core.types';
import { ManageProposalsRow } from './ManageProposalsRow';

export const ProposalsContent = () => {
  const [proposals, setProposals] = useState<AppProposal[]>([]);

  useEffect(() => {
    const fetchProposals = async () => {
      const userProposals = await getProposals();
      setProposals(userProposals);
    };

    fetchProposals();
  }, []);

  const haveProposals = proposals.length !== 0;

  return (
    <div className='m-4'>
      {haveProposals && (
        <div className='overflow-hidden max-w-[1440px] mx-auto'>
          <Table>
            <TableCaption>A list of your recent proposals.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Proposer ID</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Custom Twitter ID +Comment</TableHead>
                <TableHead>Created at</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {proposals.map((proposal) => (
                <ManageProposalsRow
                  {...{ proposal, setProposals }}
                  key={proposal.id}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
