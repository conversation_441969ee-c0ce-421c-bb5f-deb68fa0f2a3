import { Submit<PERSON>and<PERSON> } from 'react-hook-form';

import { ManageAppResourceForm } from '../ManageAppResource/ManageAppResourceForm';
import { createAppResource, deleteProposal } from '../../api';
import { AppResource, AppProposal } from '../../core.types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useState } from 'react';

type CreateAppResourceModalProps = {
  setProposals: React.Dispatch<React.SetStateAction<AppProposal[]>>;
  proposal: AppProposal;
  children: React.ReactNode;
};

export const CreateAppResourceModal = ({
  setProposals,
  proposal,
  children,
}: CreateAppResourceModalProps) => {
  const [open, setOpen] = useState(false);

  const resource = {
    title: proposal.title,
    url: proposal.url,
    tags: proposal.tags,
    authorId: proposal.authorId,
    createdAt: proposal.createdAt,
    customTwitterId: proposal.customTwitterId,
    type: proposal.type,
  } as AppResource;

  const onSubmit: SubmitHandler<AppResource> = async (values) => {
    await createAppResource(values);
    await deleteProposal(proposal.id);
    setProposals((proposals) => proposals.filter((p) => p.id !== proposal.id));

    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className='sm:max-w-[425px] max-h-[90vh] overflow-auto'
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle>Finalize proposal</DialogTitle>
        </DialogHeader>
        <ManageAppResourceForm onSubmit={onSubmit} defaultValues={resource} />
      </DialogContent>
    </Dialog>
  );
};
