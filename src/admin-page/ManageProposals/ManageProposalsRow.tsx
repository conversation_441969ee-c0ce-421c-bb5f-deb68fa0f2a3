import { deleteProposal } from '@/api/proposal.api';
import { AppConfirm } from '@/components/AppConfirm';
import { TableCell, TableRow } from '@/components/ui/table';
import c from 'classnames';
import { AiFillCheckSquare } from 'react-icons/ai';
import { AppAction } from '../../components/AppAction';
import { AppStatus } from '../../components/AppStatus';
import { useRootContext } from '../../context/useRootContext';
import { AppProposal, AppTag } from '../../core.types';
import { CreateAppResourceModal } from './CreateAppResourceModal';

type ManageProposalsRowProps = {
  proposal: AppProposal;
  setProposals: React.Dispatch<React.SetStateAction<AppProposal[]>>;
};

const regularCellStyles =
  'px-6 w-[356px] py-4 text-[#186684] font-semibold whitespace-pre-wrap dark:text-[#d4d1d1]';

export const ManageProposalsRow = ({
  proposal,
  setProposals,
}: ManageProposalsRowProps) => {
  const { tags } = useRootContext();

  const tagsList = proposal.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  );

  const onDeleteProposalConfirmed = async () => {
    const currentProposalId = proposal.id;

    if (currentProposalId === null) return;
    await deleteProposal(currentProposalId);
    setProposals((proposals) =>
      proposals.filter((p) => p.id !== currentProposalId),
    );
  };

  return (
    <TableRow key={proposal.id}>
      <TableCell className={regularCellStyles}>{proposal.proposerId}</TableCell>
      <TableCell className={regularCellStyles}>
        <a
          href={proposal.url}
          target='_blank'
          className='text-[#673ab7] hover:underline font-semibold  whitespace-pre-wrap dark:text-[#d4d1d1]'
        >
          {proposal.title}
        </a>
        <div className='flex wrap gap-2 mt-1'>
          {(tagsList as AppTag[]).map(({ name }: AppTag) => (
            <div key={name}>{name}</div>
            // <AppResourceRowPill key={name} name={name} />
          ))}
        </div>
      </TableCell>
      <TableCell className={c(regularCellStyles, 'w-[100px]')}>
        <AppStatus status={proposal.status} />
      </TableCell>
      <TableCell className={regularCellStyles}>
        {proposal.customTwitterId + ' ' + proposal.comment}
      </TableCell>
      <TableCell className={c(regularCellStyles, 'w-[100px]')}>
        {proposal.createdAt}
      </TableCell>
      <TableCell>
        <div className='flex gap-2'>
          <CreateAppResourceModal
            proposal={proposal}
            setProposals={setProposals}
          >
            <AppAction className='text-lime-600'>
              <AiFillCheckSquare />
            </AppAction>
          </CreateAppResourceModal>

          <AppConfirm
            onConfirm={onDeleteProposalConfirmed}
            message='Are you sure to Delete this proposal?'
          >
            <button className='text-red-500 hover:text-red-700'>Delete</button>
          </AppConfirm>
        </div>
      </TableCell>
    </TableRow>
  );
};
