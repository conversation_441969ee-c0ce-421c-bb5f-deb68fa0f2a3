import { useCallback } from 'react';
import { toast } from 'sonner';
import { createAuthor } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppAuthor } from '../../core.types';
import { ManageAuthorForm } from './ManageAuthorForm';
import { ManageAuthorItem } from './ManageAuthorItem';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Users } from 'lucide-react';

export const ManageAuthors = () => {
  const { setAuthors, authors } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onAuthorCreate = useCallback(
    async (values: AppAuthor) => {
      const isAlreadyAdded = authors.find(
        (author) => author.name === values.name,
      );

      if (isAlreadyAdded) {
        toast.error('Author is already added');
        return;
      }

      const tagId = await createAuthor(values as any);
      if (tagId) {
        setAuthors((prevAuthors: <AUTHORS>
          ...prevAuthors,
          { ...values, id: tagId } as AppAuthor,
        ]);
        // Refresh counters after successful creation
        refreshCounters();
      }
    },
    [authors, setAuthors, refreshCounters],
  );

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Add Author Form */}
        <div className='lg:col-span-1'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                Add New Author
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ManageAuthorForm onSubmit={onAuthorCreate} />
            </CardContent>
          </Card>
        </div>

        {/* Authors List */}
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle>Authors ({authors.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {authors.length === 0 ? (
                  <div className='text-center py-8 text-gray-500'>
                    No authors found. Add your first author to get started.
                  </div>
                ) : (
                  authors.map((author) => (
                    <ManageAuthorItem key={author.id} author={author} />
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
