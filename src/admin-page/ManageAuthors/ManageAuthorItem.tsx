import { AppConfirm } from '@/components/AppConfirm';
import { AppAuthor } from '../../core.types';
import { EditAuthorModal } from './EditAuthorModal';
import { deleteAuthor, updateAuthor } from '@/api/authors.api';
import { useRootContext } from '@/context/useRootContext';
import { useAdminCounters } from '@/context/AdminCountersContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, ExternalLink } from 'lucide-react';

type ManageAuthorItemProps = {
  author: AppAuthor;
};

export const ManageAuthorItem = ({ author }: ManageAuthorItemProps) => {
  const { setAuthors } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onDeleteAuthorConfirmed = async () => {
    if (author.id === null) return;
    await deleteAuthor(author.id);
    setAuthors((prevAuthors: <AUTHORS>
      prevAuthors.filter((x) => x.id !== author.id),
    );
    // Refresh counters after successful deletion
    refreshCounters();
  };

  const onAuthorEdit = async (values: AppAuthor) => {
    await updateAuthor({ ...values } as any);
    setAuthors((prevAuthors: <AUTHORS>
      prevAuthors.map((author) => {
        if (author.id === values.id) {
          return { ...values };
        }
        return author;
      }),
    );
  };

  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardContent className='p-4'>
        <div className='flex items-center justify-between gap-4'>
          <div className='flex items-center gap-3'>
            <img
              src={author.avatarUrl}
              alt={`${author.name} avatar`}
              className='w-10 h-10 rounded-full object-cover border-2 border-gray-200'
            />
            <div className='flex flex-col'>
              <span className='font-medium text-gray-900'>{author.name}</span>
              <a
                href={`https://twitter.com/${author.twitter}`}
                target='_blank'
                rel='noopener noreferrer'
                className='text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1'
              >
                <ExternalLink className='w-3 h-3' />@{author.twitter}
              </a>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <EditAuthorModal defaultValues={author} onSubmit={onAuthorEdit}>
              <Button variant='outline' size='sm' className='gap-2'>
                <Edit className='w-4 h-4' />
                Edit
              </Button>
            </EditAuthorModal>

            <AppConfirm
              onConfirm={onDeleteAuthorConfirmed}
              message='Are you sure you want to delete this author?'
            >
              <Button variant='destructive' size='sm' className='gap-2'>
                <Trash2 className='w-4 h-4' />
                Delete
              </Button>
            </AppConfirm>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
