import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import type { AppAuthor } from '../../core.types';
import { ManageAuthorForm } from './ManageAuthorForm';
import { useState } from 'react';

type EditAuthorModalProps = {
  onSubmit: (values: AppAuthor) => void;
  defaultValues: AppAuthor;
  children: React.ReactNode;
};

export const EditAuthorModal = ({
  defaultValues,
  children,
  onSubmit,
}: EditAuthorModalProps) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className='sm:max-w-[425px] max-h-[90vh] overflow-auto'
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle>Edit author</DialogTitle>
        </DialogHeader>
        <ManageAuthorForm
          onSubmit={(values: AppAuthor) => {
            onSubmit(values);
            setOpen(false);
          }}
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
};
