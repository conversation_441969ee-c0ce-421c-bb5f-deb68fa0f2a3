import { yup<PERSON><PERSON>olver } from '@hookform/resolvers/yup';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { AppInput } from '../../components/app-input/app-input';
import { AppSubmitButton } from '../../components/buttons/AppSubmitButton';
import { AppAuthor } from '../../core.types';

const authorSchema = yup.object({
  name: yup.string().min(1, 'Name is required'),
  twitter: yup.string().min(1, 'twitter is required'),
  avatarUrl: yup.string().url('Invalid URL'),
});

type AuthorFormData = yup.InferType<typeof authorSchema>;

export const ManageAuthorForm = ({
  onSubmit,
  defaultValues,
}: {
  onSubmit: SubmitHandler<AppAuthor>;
  defaultValues?: AppAuthor;
}) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors: errorsAuthor, isSubmitting },
  } = useForm<AuthorFormData>({
    resolver: yupResolver(authorSchema),
    defaultValues,
  });

  return (
    <FormProvider {...({ register } as any)}>
      <form
        onSubmit={handleSubmit((formData) => {
          // Convert form data to AppAuthor by adding id if it exists
          const authorData: AppAuthor = {
            id: defaultValues?.id || '', // Use existing id or empty string for new authors
            name: formData.name || '',
            twitter: formData.twitter || '',
            avatarUrl: formData.avatarUrl || '',
          };
          onSubmit(authorData);
          reset();
        })}
        className='flex-1 flex flex-col gap-2 w-[400px]]'
      >
        <AppInput
          placeholder='Name'
          name='name'
          error={errorsAuthor?.name?.message}
          type='text'
        />
        <AppInput placeholder='Twitter' name='twitter' type='text' />
        <AppInput
          placeholder='Avatar URL'
          name='avatarUrl'
          error={errorsAuthor.avatarUrl?.message}
          type='text'
        />

        <AppSubmitButton isSubmitting={isSubmitting} />
      </form>
    </FormProvider>
  );
};
