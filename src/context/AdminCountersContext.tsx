import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AdminCounters, getAllAdminCounters } from '../api/counters.api';

interface AdminCountersContextType {
  counters: AdminCounters;
  loading: boolean;
  error: string | null;
  refreshCounters: () => Promise<void>;
}

const AdminCountersContext = createContext<AdminCountersContextType | undefined>(undefined);

interface AdminCountersProviderProps {
  children: ReactNode;
}

export const AdminCountersProvider: React.FC<AdminCountersProviderProps> = ({ children }) => {
  const [counters, setCounters] = useState<AdminCounters>({
    tags: 0,
    authors: 0,
    resources: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshCounters = async () => {
    try {
      setLoading(true);
      setError(null);
      const newCounters = await getAllAdminCounters();
      setCounters(newCounters);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch counters');
      console.error('Error refreshing counters:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshCounters();
  }, []);

  const value: AdminCountersContextType = {
    counters,
    loading,
    error,
    refreshCounters,
  };

  return (
    <AdminCountersContext.Provider value={value}>
      {children}
    </AdminCountersContext.Provider>
  );
};

export const useAdminCounters = (): AdminCountersContextType => {
  const context = useContext(AdminCountersContext);
  if (context === undefined) {
    throw new Error('useAdminCounters must be used within an AdminCountersProvider');
  }
  return context;
};
