import React, { createContext, useContext, useState, useCallback } from 'react';
import type { ViewMode } from '@/components/ViewSwitcher';

interface GlobalViewContextType {
  globalViewMode: ViewMode;
  setGlobalViewMode: (mode: ViewMode) => void;
  toggleGlobalView: () => void;
}

const GlobalViewContext = createContext<GlobalViewContextType | undefined>(undefined);

export function GlobalViewProvider({ children }: { children: React.ReactNode }) {
  const [globalViewMode, setGlobalViewMode] = useState<ViewMode>('grid');

  const toggleGlobalView = useCallback(() => {
    setGlobalViewMode(current => current === 'table' ? 'grid' : 'table');
  }, []);

  return (
    <GlobalViewContext.Provider value={{
      globalViewMode,
      setGlobalViewMode,
      toggleGlobalView,
    }}>
      {children}
    </GlobalViewContext.Provider>
  );
}

export function useGlobalView() {
  const context = useContext(GlobalViewContext);
  if (!context) {
    throw new Error('useGlobalView must be used within GlobalViewProvider');
  }
  return context;
}
