import 'firebase/auth';
import {
  ReactNode,
  createContext,
  useCallback,
  useEffect,
  useState,
} from 'react';
import {
  checkIfUserIsAdmin,
  getAuthors,
  getTags,
  isFreshUpdateAvailable,
  getAllUserReadMarks,
  signOut,
  getAllUserBookmarks,
} from '../api';
import AppLoader from '../components/AppLoader/AppLoader';
import {
  AppAuthor,
  AppBookmark,
  AppTag,
  UpdateType,
  AppReadMark,
} from '../core.types';
import { firebaseAuth } from '../services/firebase-service';
import {
  getAuthorsFromLocalStorage,
  getTagsFromLocalStorage,
  getTagsUpdateIdFromLocalStorage,
  setAuthorsInLocalStorage,
  setTagsInLocalStorage,
} from '../services/utils-service';

type RootContextType = {
  isAuthorized: boolean;
  twitterImageUrl: string;
  currentUserId: string;
  hasAdminRole: boolean;
  setIsAuthorized: (isAuthorized: boolean) => void;
  logout: () => void;
  bookmarks: AppBookmark[];
  setBookmarks: React.Dispatch<React.SetStateAction<AppBookmark[]>>;
  readMarks: AppReadMark[];
  setReadMarks: React.Dispatch<React.SetStateAction<AppReadMark[]>>;
  tags: AppTag[];
  authors: AppAuthor[];
  setAuthors: <AUTHORS>
  setTags: React.Dispatch<React.SetStateAction<AppTag[]>>;
};

// Create a Context
export const RootContext = createContext<RootContextType>(null as never);

// Create a Provider Component
export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [currentUserId, setCurrentUserId] = useState<string>('');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [twitterImageUrl, setTwitterImageUrl] = useState('');
  const [hasAdminRole, setHasAdminRole] = useState(false);
  const [tags, setTags] = useState<AppTag[]>([]);
  const [authors, setAuthors] = useState<AppAuthor[]>([]);
  const [loading, setLoading] = useState(false);
  const [bookmarks, setBookmarks] = useState<AppBookmark[]>([]);
  const [readMarks, setReadMarks] = useState<AppReadMark[]>([]);

  // Monitor auth state and set isAuthorized accordingly
  useEffect(() => {
    const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
      setIsAuthorized(!!user); // If user is not null, isAuthorized is true

      if (user) {
        // User is signed in.
        const twitterImageUrl = user?.photoURL ?? '';
        setTwitterImageUrl(twitterImageUrl);
        setCurrentUserId(user.uid);
        checkIfUserIsAdmin().then((isAdmin) => setHasAdminRole(isAdmin));
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const getTagsFromServer = useCallback(async () => {
    const tags = await getTags();
    return tags;
  }, []);

  const getAuthorsFromServer = useCallback(async () => {
    const authors = await getAuthors();
    return authors;
  }, []);

  const getInitialData = useCallback(async () => {
    setLoading(true);
    const currentTagsUpdateTag = getTagsUpdateIdFromLocalStorage();
    const currentAuthorsUpdateTag = getTagsUpdateIdFromLocalStorage();
    const tagsFromLocalStorage = getTagsFromLocalStorage();
    const authorsFromLocalStorage = getAuthorsFromLocalStorage();
    const isTagsUpdateAvailablePromise = isFreshUpdateAvailable(
      currentTagsUpdateTag,
      UpdateType.Tag,
    );
    const isAuthorsUpdateAvailablePromise = isFreshUpdateAvailable(
      currentAuthorsUpdateTag,
      UpdateType.Author,
    );
    const [isTagsUpdateAvailable, isAuthorsUpdateAvailable] = await Promise.all(
      [isTagsUpdateAvailablePromise, isAuthorsUpdateAvailablePromise],
    );

    if (isTagsUpdateAvailable || !tagsFromLocalStorage.length) {
      const tags = await getTagsFromServer();
      setTags(tags);
      setTagsInLocalStorage(tags);
    } else {
      setTags(tagsFromLocalStorage);
    }

    if (isAuthorsUpdateAvailable || !authorsFromLocalStorage.length) {
      const authors = await getAuthorsFromServer();
      setAuthors(authors);
      setAuthorsInLocalStorage(authors);
    } else {
      setAuthors(authorsFromLocalStorage);
    }
    setLoading(false);
  }, [getAuthorsFromServer, getTagsFromServer]);

  const getBookmarks = useCallback(async () => {
    const bookmarks: AppBookmark[] = await getAllUserBookmarks();
    setBookmarks(bookmarks);
  }, []);

  const getReadMarks = useCallback(async () => {
    const readMarks: AppReadMark[] = await getAllUserReadMarks();
    setReadMarks(readMarks);
  }, []);

  useEffect(() => {
    getInitialData();
  }, [getInitialData]);

  useEffect(() => {
    if (isAuthorized) {
      getBookmarks();
      getReadMarks();
    }
  }, [getBookmarks, getReadMarks, isAuthorized]);

  const logout = useCallback(async () => {
    await signOut();
    setIsAuthorized(false);
    setTwitterImageUrl('');
  }, []);

  if (authors.length && tags.length) {
    console.log({
      authors: authors.map((x) => ({
        id: x.id,
        name: x.name,
      })),
      tags: tags.map((x) => ({
        id: x.id,
        name: x.name,
      })),
    });
  }

  return (
    <RootContext.Provider
      value={{
        currentUserId,
        isAuthorized,
        setBookmarks,
        setIsAuthorized,
        twitterImageUrl,
        hasAdminRole,
        logout,
        tags,
        bookmarks,
        readMarks,
        setReadMarks,
        authors,
        setAuthors,
        setTags,
      }}
    >
      {loading ? <AppLoader /> : children}
    </RootContext.Provider>
  );
};
