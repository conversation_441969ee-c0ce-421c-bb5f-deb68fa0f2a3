import { FaGoogle } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { signInWithGoogle, signInWithTwitter } from '../../api';
import { AppButtonSecondary } from '../buttons/app-button-new';

export const SignInButtons = () => {
  return (
    <div className='flex gap-1'>
      <AppButtonSecondary onClick={signInWithTwitter}>
        Login
        <FaXTwitter size={20} />
      </AppButtonSecondary>
      <AppButtonSecondary onClick={signInWithGoogle}>
        Login
        <FaGoogle size={20} />
      </AppButtonSecondary>
    </div>
  );
};
