import { MessageCircle } from 'lucide-react';
import { AppButtonSecondary } from '../buttons/app-button-new';

export const ShareButton = ({ className }: { className?: string }) => {
  const shareText = encodeURIComponent(
    '😮Wow! @0<PERSON><PERSON><PERSON><PERSON> created super useful site \n 🚀🚀🚀smartsechub.online 🚀🚀🚀. Now I can search smart contract security info easily across all blogs!!!. Join to explore✅! #SmartContractSecurity #SmartSecHub',
  );

  const handleShare = () => {
    window.open(`https://twitter.com/intent/tweet?text=${shareText}`, '_blank');
  };

  return (
    <AppButtonSecondary onClick={handleShare} className={className}>
      <MessageCircle size={20} className='h-4 w-4' />
    </AppButtonSecondary>
  );
};
