# ResourceTable Component

A reusable, feature-rich table component for displaying AppResource data with support for sorting, filtering, search, and view switching.

## Features

- ✅ **Sortable columns** - Click column headers to sort data
- ✅ **Search functionality** - Search across title, description, and tags
- ✅ **View switching** - Toggle between table and grid views
- ✅ **Responsive design** - Works on all screen sizes
- ✅ **Loading states** - Built-in loading and empty state handling
- ✅ **Customizable columns** - Define your own column configurations
- ✅ **Pre-built configs** - Ready-to-use configurations for different resource types
- ✅ **Action buttons** - Like, dislike, bookmark, and read mark actions
- ✅ **Type-specific icons** - Visual indicators for different resource types

## Basic Usage

```tsx
import { ResourceTable } from '@/components/ResourceTable';
import { useAppResourcesContext } from '../app-resources-context';
import { useTableView } from '@/hooks/useTableView';

export const MyTab = () => {
  const { resources, loading, loadingMarkup } = useAppResourcesContext();
  const { viewMode, setViewMode } = useTableView('grid');

  if (viewMode === 'table') {
    return (
      <div className="p-4" style={{ height: 'calc(100vh - 117px)', overflowY: 'auto' }}>
        <ResourceTable
          resources={resources}
          loading={loading}
          loadingMarkup={loadingMarkup}
          currentView={viewMode}
          onViewChange={setViewMode}
        />
      </div>
    );
  }

  // Your grid view component here
  return <div>Grid view...</div>;
};
```

## Using Pre-built Configurations

```tsx
import { ResourceTable, getTableConfigForResourceType } from '@/components/ResourceTable';
import { AppResourceType } from '@/core.types';

// Use resource-type specific configuration
<ResourceTable
  resources={resources}
  columns={getTableConfigForResourceType(AppResourceType.Github)}
  currentView={viewMode}
  onViewChange={setViewMode}
/>

// Or use specific configurations directly
import { githubTableConfig, tweetTableConfig, articleTableConfig } from '@/components/ResourceTable';

<ResourceTable
  resources={resources}
  columns={githubTableConfig}
  currentView={viewMode}
  onViewChange={setViewMode}
/>
```

## Custom Column Configuration

```tsx
import { ResourceTableColumn } from '@/components/ResourceTable';

const customColumns: ResourceTableColumn[] = [
  {
    key: 'title',
    label: 'Resource Title',
    sortable: true,
    render: (resource) => (
      <div>
        <h3>{resource.title}</h3>
        <p>{resource.description}</p>
      </div>
    ),
  },
  {
    key: 'author',
    label: 'Author',
    render: (resource) => <span>{resource.authorId}</span>,
  },
  // Add more columns as needed
];

<ResourceTable
  resources={resources}
  columns={customColumns}
  currentView={viewMode}
  onViewChange={setViewMode}
/>
```

## Available Pre-built Configurations

- `defaultTableConfig` - General purpose configuration with all columns
- `compactTableConfig` - Minimal configuration for small spaces
- `articleTableConfig` - Optimized for articles and papers
- `videoTableConfig` - Includes video-specific columns
- `githubTableConfig` - Shows repository information
- `tweetTableConfig` - Optimized for tweet display

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `resources` | `AppResource[]` | Required | Array of resources to display |
| `loading` | `boolean` | `false` | Show loading state |
| `loadingMarkup` | `React.ReactNode` | - | Custom loading component |
| `columns` | `ResourceTableColumn[]` | `defaultColumns` | Column configuration |
| `searchable` | `boolean` | `true` | Enable search functionality |
| `sortable` | `boolean` | `true` | Enable column sorting |
| `showViewToggle` | `boolean` | `true` | Show table/grid view toggle |
| `onViewChange` | `(view: 'table' \| 'grid') => void` | - | View change handler |
| `currentView` | `'table' \| 'grid'` | `'table'` | Current view mode |
| `className` | `string` | - | Additional CSS classes |

## Column Configuration

```tsx
interface ResourceTableColumn {
  key: string;                                    // Unique identifier
  label: string;                                  // Column header text
  sortable?: boolean;                            // Enable sorting
  render?: (resource: AppResource) => React.ReactNode; // Custom render function
  className?: string;                            // Additional CSS classes
}
```

## Integration with useTableView Hook

The `useTableView` hook provides state management for switching between table and grid views:

```tsx
import { useTableView } from '@/hooks/useTableView';

const { viewMode, setViewMode, toggleView, isTableView, isGridView } = useTableView('grid');
```

## Styling

The component uses Tailwind CSS and shadcn/ui components. It automatically adapts to your theme (light/dark mode).

## Examples

See the implementation in:
- `src/search-page/search-tabs/article-tab.tsx`
- `src/search-page/search-tabs/github-tab.tsx`
- `src/search-page/search-tabs/sites-tab.tsx`
- `src/search-page/search-tabs/videos-tab.tsx`
- `src/search-page/search-tabs/tweets-tab.tsx`
