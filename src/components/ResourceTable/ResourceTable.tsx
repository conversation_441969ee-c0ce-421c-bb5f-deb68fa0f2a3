import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { AppResource } from '@/core.types';
import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar/AppAuthorNameAvatar';
import { LikeAction } from '@/search-page/resource-actions/like-action';
import { DislikeAction } from '@/search-page/resource-actions/dislike-action';
import { BookmarkAction } from '@/search-page/resource-actions/bookmark-action';
import { ReadMarkAction } from '@/search-page/resource-actions/readmark-action';
import { firebaseTimestampToFriendlyDate } from '@/services/utils-service';
import { useRootContext } from '@/context/useRootContext';
import { ArrowUpDown, Search, Grid, List } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ResourceTableSkeleton } from '@/components/skeletons/TableSkeletons';

// Author cell component that uses the context
const AuthorCell = ({ resource }: { resource: AppResource }) => {
  const { authors } = useRootContext();
  const author = authors.find((a) => a.id === resource.authorId);
  return (
    <AppAuthorNameAvatar
      author={author}
      nameLength={100}
      customTwitterId={resource.customTwitterId}
    />
  );
};

export interface ResourceTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (resource: AppResource) => React.ReactNode;
  className?: string;
}

interface ResourceTableProps {
  resources: AppResource[];
  loading?: boolean;
  loadingMarkup?: React.ReactNode;
  columns?: ResourceTableColumn[];
  searchable?: boolean;
  sortable?: boolean;
  showViewToggle?: boolean;
  onViewChange?: (view: 'table' | 'grid') => void;
  currentView?: 'table' | 'grid';
  className?: string;
}

// Tags cell component that uses the context to get tag names
const TagsCell = ({ resource }: { resource: AppResource }) => {
  const { tags } = useRootContext();
  const resourceTags = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean);

  return (
    <div className='flex flex-wrap gap-1 mt-2'>
      {resourceTags.slice(0, 3).map((tag) => (
        <Badge key={tag!.id} variant='outline' className='text-xs'>
          {tag!.name}
        </Badge>
      ))}
      {resourceTags.length > 3 && (
        <Badge variant='outline' className='text-xs'>
          +{resourceTags.length - 3}
        </Badge>
      )}
    </div>
  );
};

const defaultColumns: ResourceTableColumn[] = [
  {
    key: 'title',
    label: 'Title',
    sortable: true,
    render: (resource) => (
      <div className='max-w-md'>
        <a
          href={resource.url}
          target='_blank'
          rel='noopener noreferrer'
          className='text-foreground hover:text-primary underline font-medium block mb-1'
        >
          {resource.title}
        </a>
        {resource.description && (
          <p className='text-sm text-muted-foreground mb-1 line-clamp-2'>
            {resource.description}
          </p>
        )}
        <TagsCell resource={resource} />
      </div>
    ),
  },
  {
    key: 'author',
    label: 'Author',
    render: (resource) => <AuthorCell resource={resource} />,
  },
  // {
  //   key: 'engagement',
  //   label: 'Engagement',
  //   render: (resource) => (
  //     <div className='flex items-center gap-2'>
  //       <span className='text-sm text-muted-foreground'>
  //         👍 {resource.likesCount || 0}
  //       </span>
  //       <span className='text-sm text-muted-foreground'>
  //         👎 {resource.dislikesCount || 0}
  //       </span>
  //     </div>
  //   ),
  // },
  {
    key: 'createdAt',
    label: 'Created',
    sortable: true,
    render: (resource) => (
      <span className='text-sm text-muted-foreground'>
        {firebaseTimestampToFriendlyDate(resource.createdAt as any)}
      </span>
    ),
  },
  {
    key: 'actions',
    label: 'Actions',
    render: (resource) => (
      <div className='flex items-center gap-1'>
        <LikeAction resource={resource} />
        <DislikeAction resource={resource} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
      </div>
    ),
  },
];

export function ResourceTable({
  resources,
  loading = false,
  loadingMarkup,
  columns = defaultColumns,
  searchable = true,
  sortable = true,
  showViewToggle = true,
  onViewChange,
  currentView = 'table',
  className,
}: ResourceTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  const filteredAndSortedResources = useMemo(() => {
    let filtered = resources;

    // Filter by search term
    if (searchTerm) {
      filtered = resources.filter(
        (resource) =>
          resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          resource.description
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          resource.tags.some((tag) =>
            tag.toLowerCase().includes(searchTerm.toLowerCase()),
          ),
      );
    }

    // Sort
    if (sortConfig && sortable) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortConfig.key as keyof AppResource];
        const bValue = b[sortConfig.key as keyof AppResource];

        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [resources, searchTerm, sortConfig, sortable]);

  const handleSort = (key: string) => {
    if (!sortable) return;

    setSortConfig((current) => {
      if (current?.key === key) {
        return {
          key,
          direction: current.direction === 'asc' ? 'desc' : 'asc',
        };
      }
      return { key, direction: 'asc' };
    });
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with search and view toggle */}
      <div className='flex items-center justify-between gap-4'>
        {searchable && (
          <div className='relative flex-1 max-w-sm'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <Input
              placeholder='Search resources...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        )}

        {showViewToggle && onViewChange && (
          <div className='flex items-center gap-2'>
            <Button
              variant={currentView === 'table' ? 'default' : 'outline'}
              size='sm'
              onClick={() => onViewChange('table')}
            >
              <List className='h-4 w-4' />
            </Button>
            <Button
              variant={currentView === 'grid' ? 'default' : 'outline'}
              size='sm'
              onClick={() => onViewChange('grid')}
            >
              <Grid className='h-4 w-4' />
            </Button>
          </div>
        )}
      </div>

      {/* Table */}
      <div className='rounded-md border'>
        {loading && resources.length === 0 ? (
          <ResourceTableSkeleton columns={columns.length} rows={8} />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead
                    key={column.key}
                    className={cn(
                      column.className,
                      column.sortable &&
                        sortable &&
                        'cursor-pointer hover:bg-muted/50',
                    )}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className='flex items-center gap-2'>
                      {column.label}
                      {column.sortable && sortable && (
                        <ArrowUpDown className='h-4 w-4' />
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedResources.length === 0 && !loading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className='text-center py-8'
                  >
                    {searchTerm
                      ? 'No resources found matching your search.'
                      : 'No resources available.'}
                  </TableCell>
                </TableRow>
              ) : null}
              {filteredAndSortedResources.map((resource) => (
                <TableRow
                  key={resource.id}
                  className='hover:bg-muted/50 h-auto'
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.key}
                      className={cn('py-3', column.className)}
                    >
                      {column.render
                        ? column.render(resource)
                        : String(
                            resource[column.key as keyof AppResource] || '',
                          )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Lazy loading trigger */}
      {loadingMarkup}

      {/* Results count */}
      {!loading && (
        <div className='text-sm text-muted-foreground'>
          Showing {filteredAndSortedResources.length} of {resources.length}{' '}
          resources
          {searchTerm && ` for "${searchTerm}"`}
        </div>
      )}
    </div>
  );
}
