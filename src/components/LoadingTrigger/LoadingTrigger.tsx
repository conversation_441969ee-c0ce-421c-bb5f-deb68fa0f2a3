import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingTriggerProps {
  isLoading: boolean;
  hasMore: boolean;
  loadMoreRef: (node?: Element | null) => void;
  className?: string;
  loadingText?: string;
  endText?: string;
}

export function LoadingTrigger({
  isLoading,
  hasMore,
  loadMoreRef,
  className,
  loadingText = 'Loading more...',
  endText = 'No more items to load',
}: LoadingTriggerProps) {
  if (!hasMore && !isLoading) {
    return (
      <div className={cn('flex justify-center py-4 text-sm text-muted-foreground', className)}>
        {endText}
      </div>
    );
  }

  return (
    <div
      ref={loadMoreRef}
      className={cn('flex items-center justify-center py-4', className)}
    >
      {isLoading && (
        <>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">{loadingText}</span>
        </>
      )}
    </div>
  );
}
