import { AppAuthor } from '../../core.types';

type AppAuthorNameAvatarProps = {
  author?: AppAuthor;
  customTwitterId?: string;
  nameLength?: number;
};

export const AppAuthorNameAvatar = ({
  author,
  customTwitterId,
  nameLength = 130,
}: AppAuthorNameAvatarProps) => {
  const onClick = () => {
    window.open(
      `https://twitter.com/${author?.twitter || customTwitterId}`,
      '_blank',
    );
  };

  if (!author && !customTwitterId) return null;

  return (
    <span
      title={author?.name || customTwitterId}
      style={{ display: `inline !important` }}
      rel='noopener noreferrer'
      onClick={onClick}
      className='break-normal text-xs font-semibold hover:underline text-[#673ab7] dark:text-[#d4d1d1]'
    >
      {author && (
        <img
          src={author.avatarUrl}
          alt='no logo'
          className='rounded-[50%] object-cover w-[20px] h-[20px] inline mr-2'
        />
      )}

      <span
        className={`mobile:hidden tablet:block text-ellipsis whitespace-nowrap w-[${nameLength}px] inline overflow-hidden`}
      >
        {author?.name || customTwitterId}
      </span>
    </span>
  );
};
