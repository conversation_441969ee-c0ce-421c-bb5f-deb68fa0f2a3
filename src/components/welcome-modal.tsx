import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { isInitialWelcomeModalShown } from '@/services/utils-service';
import { InfoIcon } from 'lucide-react';
import { useState } from 'react';
import { AppButtonSecondary } from './buttons/app-button-new';

export function WelcomeModal() {
  const [open, setOpen] = useState(!isInitialWelcomeModalShown());

  return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <InfoIcon className='cursor-pointer' />
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Welcome!</DialogTitle>
        </DialogHeader>
        <div className='flex flex-col gap-2 py-4 shadow'>
          <p>
            🔍 You can easily search for web3sec resources(articles, videos,
            tweets, etc.) on our hub!
          </p>
          <p>🚀 Want to add your own resource?</p>
          <ol>
            <li>1. Register.</li>
            <li>2. Hit the plus button.</li>
            <li>
              3. Fill out the form with your resource proposal and send it.
            </li>
            <li className='font-semibold'>
              Note: No more than 5 pending proposals at a time.
            </li>
          </ol>

          <p>
            👀 Keep track of your proposals in the{' '}
            <span className='font-semibold'> Profile menu.</span>
          </p>
          <p>📌 You can also 💾 links, give them 👍 and 👎</p>
        </div>
        <DialogFooter>
          <AppButtonSecondary onClick={() => setOpen(false)}>
            Got it!
          </AppButtonSecondary>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
