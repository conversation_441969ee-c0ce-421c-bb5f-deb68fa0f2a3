import React from 'react';
import { AppProposalStatus } from '../core.types';

interface AppStatusProps {
  status: AppProposalStatus;
}

export const AppStatus: React.FC<AppStatusProps> = ({ status }) => {
  const statusColor = () => {
    switch (status) {
      case AppProposalStatus.Pending:
        return 'bg-yellow-500';
      case AppProposalStatus.Approved:
        return 'bg-green-500';
      case AppProposalStatus.Rejected:
        return 'bg-red-500';
      default:
        return 'ng-gray-500';
    }
  };

  return (
    <span
      className={`font-semibold ${statusColor()} inline-block p-1 rounded-md text-black`}
    >
      {status.toUpperCase()}
    </span>
  );
};
