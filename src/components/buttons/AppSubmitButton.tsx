import { Spinner } from '../spinner';
import { AppButtonSecondary } from './app-button-new';

type AppSubmitButtonProps = {
  isSubmitting: boolean;
};

export const AppSubmitButton = ({ isSubmitting }: AppSubmitButtonProps) => {
  return (
    <div className='flex gap-2'>
      <AppButtonSecondary type='submit' disabled={isSubmitting}>
        <div className='flex gap-2 justify-center'>
          {' '}
          {isSubmitting && <Spinner />}
          Submit
        </div>
      </AppButtonSecondary>
    </div>
  );
};
