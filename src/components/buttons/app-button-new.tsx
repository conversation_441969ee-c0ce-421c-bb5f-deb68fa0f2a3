import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import noop from 'lodash/noop';
import type { ReactNode } from 'react';

export const AppButtonSecondary = ({
  onClick = noop,
  children,
  type = 'button',
  disabled,
  className,
}: {
  type?: 'button' | 'submit' | 'reset';
  onClick?: () => void;
  children: ReactNode;
  disabled?: boolean;
  className?: string;
}) => {
  return (
    <Button
      disabled={disabled}
      type={type}
      className={cn(
        'text-black dark:text-white hover:bg-transparent bg-transparent rounded-[25px] flex gap-1 items-center justify-center border-solid border-[3px] cursor-pointer',
        'border-slate-400 bg-transparent text-slate-400  hover:border-yellow-600 hover:text-yellow-600 dark:hover:text-yellow-600',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </Button>
  );
};
