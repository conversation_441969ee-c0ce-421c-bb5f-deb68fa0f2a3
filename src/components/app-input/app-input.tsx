import { useFormContext } from 'react-hook-form';

import { Input } from '@/components/ui/input';

type AppInputProps = {
  className?: string;
  type: string;
  error?: string;
  name: string;
} & React.InputHTMLAttributes<HTMLInputElement>;

export const AppInput = ({
  placeholder,
  className,
  type,
  error,
  name,
  ...rest
}: AppInputProps) => {
  const { register } = useFormContext();

  return (
    <div className='h-[55px] relative'>
      <Input
        className={className}
        type={type}
        placeholder={placeholder}
        {...register(name)}
        {...rest}
      />
      {error && (
        <p className='text-red-500 text-[10px] font-semibold pl-1'>{error}</p>
      )}
    </div>
  );
};
