import { useFormContext } from 'react-hook-form';

import { Textarea } from '@/components/ui/textarea';

type AppInputProps = {
  className?: string;
  error?: string;
  name: string;
} & React.InputHTMLAttributes<HTMLTextAreaElement>;

export const AppTextarea = ({
  placeholder,
  className,
  error,
  name,
  ...rest
}: AppInputProps) => {
  const { register } = useFormContext();

  return (
    <div>
      <Textarea
        className={className}
        placeholder={placeholder}
        {...register(name)}
        {...rest}
      />
      {error && (
        <p className='text-red-500 text-[15px] font-semibold pl-1'>{error}</p>
      )}
    </div>
  );
};
