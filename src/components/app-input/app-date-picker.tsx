import { useFormContext } from 'react-hook-form';

import { cn } from '@/utils';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useEffect, useState } from 'react';

type AppDatePickerProps = {
  className?: string;
  name: string;
  placeholder: string;
};

export const AppDatePicker = ({
  placeholder,
  className,
  name,
}: AppDatePickerProps) => {
  const { register, watch, setValue } = useFormContext();
  const date = watch(name);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    register(name);
  }, [register, name]);

  return (
    <div className={cn(className, 'h-[55px] relative w-full')}>
      <Popover open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'w-full justify-start text-left font-normal',
              !date && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className='mr-2 h-4 w-4' />

            {date ? (
              new Date(date).toLocaleDateString()
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-auto p-0'>
          <Calendar
            mode='single'
            selected={date}
            onSelect={(date) => {
              setValue(name, date);
              setIsOpen(false);
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};
