import React from 'react';
import { Button } from '@/components/ui/button';
import { Grid, List } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ViewMode = 'table' | 'grid';

interface ViewSwitcherProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
}

export function ViewSwitcher({ currentView, onViewChange, className }: ViewSwitcherProps) {
  return (
    <div className={cn('flex items-center gap-1 bg-muted rounded-md p-1', className)}>
      <Button
        variant={currentView === 'grid' ? 'default' : 'ghost'}
        size='sm'
        onClick={() => onViewChange('grid')}
        className="h-8 px-3"
      >
        <Grid className='h-4 w-4 mr-1' />
        Grid
      </Button>
      <Button
        variant={currentView === 'table' ? 'default' : 'ghost'}
        size='sm'
        onClick={() => onViewChange('table')}
        className="h-8 px-3"
      >
        <List className='h-4 w-4 mr-1' />
        Table
      </Button>
    </div>
  );
}
