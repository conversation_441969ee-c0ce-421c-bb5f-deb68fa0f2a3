import { AppSelect } from '../AppSelect';
import { AppInput } from '../app-input/app-input';
import { AppSubmitButton } from '../buttons/AppSubmitButton';
import { AppTextarea } from '../app-input/app-textarea';
import { AppAuthorSelect } from '../AppSelect/AppAuthorSelect';
import * as yup from 'yup';
import { useRootContext } from '../../context/useRootContext';
import { yupResolver } from '@hookform/resolvers/yup';

import { FormProvider, useForm } from 'react-hook-form';
import {
  AppProposal,
  AppResourceType,
  AppTag,
  ResourceTypeOptions,
  TagGroup,
  TagGroupName,
} from '../../core.types';
import { useMemo, useEffect } from 'react';
import { AppRadioGroup } from '../app-radio-group/app-radio-group';
import { groupTags } from '../../services/utils-service';
import { AppDatePicker } from '../app-input/app-date-picker';

type ProposalFormProps = {
  onSubmit(...rest: any[]): Promise<void>;
  defaultValues?: AppProposal;
};

const proposalSchema = yup.object({
  title: yup.string().min(1, 'Title is required'),
  url: yup.string().min(1, 'URL is required'),
  createdAt: yup.string().min(1, 'Date is required'),
  comment: yup.string().max(200, 'Comment is too long'),
});

export const ProposalForm = ({
  onSubmit,
  defaultValues,
}: ProposalFormProps) => {
  const { tags } = useRootContext();
  const {
    register,
    watch,
    reset,
    setValue,
    handleSubmit,
    formState: { errors: errorsProposal, isSubmitting },
  } = useForm<AppProposal>({
    resolver: yupResolver(proposalSchema as any),
    defaultValues,
  });

  const groupedTags = groupTags(tags);

  const groupedOptions = useMemo(() => {
    return Object.entries(groupedTags).map(([group, tagsInGroup]) => ({
      label: TagGroupName[group as TagGroup],
      options: (tagsInGroup as AppTag[]).map(({ id, name }) => ({
        value: id,
        label: name,
      })),
    }));
  }, [groupedTags]);

  const authorId = watch('authorId');
  const selectedTags = watch('tags');
  const typeValue = watch('type');

  const onAuthorChange = (authorId: string) => {
    setValue('authorId', authorId);
  };

  const onTagsChange = (tags: string[]) => {
    setValue('tags', tags);
  };

  const onChangeType = (value: string) => {
    setValue('type', value as AppResourceType);
  };

  useEffect(() => {
    register('authorId');
    register('tags');
    register('type');
  }, [register]);

  return (
    <FormProvider {...({ register, watch, setValue } as any)}>
      <form
        onSubmit={handleSubmit(async (...rest) => {
          await onSubmit(...rest);
          reset();
        })}
        className='flex-1 flex flex-col gap-2'
      >
        <AppAuthorSelect value={authorId} onChange={onAuthorChange} />
        <AppInput
          type='text'
          name='customTwitterId'
          placeholder='Or write author @twitterId'
        />
        <AppInput
          type='text'
          name='title'
          placeholder='Title'
          error={errorsProposal.title?.message}
        />
        <AppInput
          type='text'
          name='url'
          placeholder='URL'
          error={errorsProposal.url?.message}
        />
        <AppRadioGroup
          label='Resource type:'
          value={typeValue}
          onChange={onChangeType}
          options={ResourceTypeOptions as any[]}
        />
        <AppSelect
          placeholder='Select tags'
          value={selectedTags}
          options={groupedOptions}
          onChange={onTagsChange}
          multiple
        />
        <AppDatePicker name='createdAt' placeholder='Publish date' />
        <AppTextarea
          name='comment'
          placeholder={`Comment: \ne.g. write missed tags you want to add`}
        />
        <AppSubmitButton isSubmitting={isSubmitting} />
      </form>
    </FormProvider>
  );
};
