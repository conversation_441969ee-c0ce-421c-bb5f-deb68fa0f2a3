import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

interface AppMetadataProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
}

export function AppMetadata({ 
  title, 
  description, 
  keywords, 
  image 
}: AppMetadataProps) {
  const location = useLocation();
  
  const defaultTitle = 'SmartSecHub 🇺🇦 - Smart Contract Security Resources Hub';
  const defaultDescription = 'Discover and explore the best smart contract security resources. Search across articles, videos, tweets, GitHub repositories, and research papers from top security experts and researchers.';
  const defaultKeywords = 'smart contract security, blockchain security, ethereum security, solidity security, defi security, web3 security, smart contract audit, blockchain audit, security research, cryptocurrency security';
  const defaultImage = 'https://smartsechub.online/og-image.png';
  
  const finalTitle = title ? `${title} | SmartSecHub 🇺🇦` : defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalKeywords = keywords || defaultKeywords;
  const finalImage = image || defaultImage;
  const canonicalUrl = `https://smartsechub.online${location.pathname}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content="Mike Rudenko (@0xmrudenko)" />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="SmartSecHub" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@0xmrudenko" />
      <meta name="twitter:creator" content="@0xmrudenko" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#673ab7" />
      <meta name="msapplication-TileColor" content="#673ab7" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="SmartSecHub" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "SmartSecHub",
          "description": finalDescription,
          "url": "https://smartsechub.online",
          "author": {
            "@type": "Person",
            "name": "Mike Rudenko",
            "url": "https://twitter.com/0xmrudenko"
          },
          "keywords": finalKeywords,
          "inLanguage": "en-US",
          "copyrightYear": new Date().getFullYear(),
          "genre": "Technology",
          "audience": {
            "@type": "Audience",
            "audienceType": "Developers, Security Researchers, Blockchain Enthusiasts"
          }
        })}
      </script>
    </Helmet>
  );
}
