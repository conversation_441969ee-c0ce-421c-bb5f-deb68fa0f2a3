import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Bookmark, BookmarkCheck } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BookmarksToggleProps {
  isActive: boolean;
  onToggle: (value: boolean) => void;
  className?: string;
}

export function BookmarksToggle({ isActive, onToggle, className }: BookmarksToggleProps) {
  return (
    <div className={cn('flex items-center space-x-3 p-2 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors', className)}>
      <div className="flex items-center space-x-2">
        <Switch
          id="bookmarks-toggle"
          checked={isActive}
          onCheckedChange={onToggle}
          className="data-[state=checked]:bg-yellow-500"
        />
        <Label 
          htmlFor="bookmarks-toggle" 
          className="flex items-center gap-2 cursor-pointer text-sm font-medium"
        >
          <div className="flex items-center gap-1.5">
            {isActive ? (
              <>
                <BookmarkCheck className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <span className="text-yellow-700 dark:text-yellow-300">My Bookmarks</span>
              </>
            ) : (
              <>
                <Bookmark className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Show Bookmarks</span>
              </>
            )}
          </div>
        </Label>
      </div>
      
      {isActive && (
        <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
          <span className="text-xs text-yellow-700 dark:text-yellow-300 font-medium">
            Active
          </span>
        </div>
      )}
    </div>
  );
}
