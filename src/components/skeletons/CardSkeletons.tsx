import { GlowingEffect } from '@/components/ui/glowing-effect';
import { Skeleton } from '@/components/ui/skeleton';

// Article Card Skeleton
export const ArticleCardSkeleton = () => (
  <div className='rounded-lg border relative group'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <Skeleton className='h-[400px] w-full rounded-lg' />
  </div>
);

// GitHub Card Skeleton
export const GithubCardSkeleton = () => (
  <div className='rounded-lg border relative group'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <Skeleton className='h-[500px] w-full rounded-lg' />
  </div>
);

// Video Card Skeleton
export const VideoCardSkeleton = () => (
  <div className='rounded-lg border relative group'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <Skeleton className='h-[600px] w-full rounded-lg' />
  </div>
);

// Site Card Skeleton
export const SiteCardSkeleton = () => (
  <div className='rounded-xl border relative group'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <Skeleton className='h-[300px] w-full rounded-xl' />
  </div>
);

// Tweet Card Skeleton
export const TweetCardSkeleton = () => (
  <div className='rounded-xl border relative group'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <Skeleton className='h-[250px] w-full rounded-xl' />
  </div>
);
