import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';

// Resource Table Skeleton
export const ResourceTableSkeleton = ({
  columns = 6,
  rows = 8,
}: {
  columns?: number;
  rows?: number;
}) => (
  <Table>
    <TableHeader>
      <TableRow>
        {Array(columns)
          .fill(0)
          .map((_, index) => (
            <TableHead key={index}>
              <Skeleton className='h-4 w-20' />
            </TableHead>
          ))}
      </TableRow>
    </TableHeader>
    <TableBody>
      {Array(rows)
        .fill(0)
        .map((_, rowIndex) => (
          <TableRow key={rowIndex} className='hover:bg-muted/50 h-auto'>
            {Array(columns)
              .fill(0)
              .map((_, colIndex) => (
                <TableCell key={colIndex} className='py-3'>
                  {colIndex === 0 ? (
                    // Title column - larger content
                    <div className='space-y-2'>
                      <Skeleton className='h-4 w-3/4' />
                      <div className='flex flex-wrap gap-1'>
                        <Skeleton className='h-5 w-12' />
                        <Skeleton className='h-5 w-16' />
                      </div>
                    </div>
                  ) : colIndex === 1 ? (
                    // Author column
                    <div className='flex items-center gap-2'>
                      <Skeleton className='h-6 w-6 rounded-full' />
                      <Skeleton className='h-4 w-20' />
                    </div>
                  ) : colIndex === 2 ? (
                    // Type column
                    <Skeleton className='h-6 w-16' />
                  ) : colIndex === 3 ? (
                    // Engagement column
                    <div className='flex items-center gap-2'>
                      <Skeleton className='h-4 w-8' />
                      <Skeleton className='h-4 w-8' />
                    </div>
                  ) : colIndex === 4 ? (
                    // Date column
                    <Skeleton className='h-4 w-16' />
                  ) : (
                    // Actions column
                    <div className='flex items-center gap-1'>
                      <Skeleton className='h-6 w-6' />
                      <Skeleton className='h-6 w-6' />
                      <Skeleton className='h-6 w-6' />
                      <Skeleton className='h-6 w-6' />
                    </div>
                  )}
                </TableCell>
              ))}
          </TableRow>
        ))}
    </TableBody>
  </Table>
);

// Proposal Table Skeleton
export const ProposalTableSkeleton = ({ rows = 5 }: { rows?: number }) => (
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>
          <Skeleton className='h-4 w-12' />
        </TableHead>
        <TableHead>
          <Skeleton className='h-4 w-20' />
        </TableHead>
        <TableHead>
          <Skeleton className='h-4 w-16' />
        </TableHead>
        <TableHead>
          <Skeleton className='h-4 w-16' />
        </TableHead>
        <TableHead>
          <Skeleton className='h-4 w-20' />
        </TableHead>
        <TableHead>
          <Skeleton className='h-4 w-16' />
        </TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      {Array(rows)
        .fill(0)
        .map((_, index) => (
          <TableRow key={index}>
            <TableCell className='font-medium'>
              <Skeleton className='h-4 w-3/4' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-20' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-6 w-16' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-24' />
            </TableCell>
            <TableCell>
              <Skeleton className='h-4 w-16' />
            </TableCell>
            <TableCell>
              <div className='flex gap-2'>
                <Skeleton className='h-8 w-8' />
                <Skeleton className='h-8 w-8' />
              </div>
            </TableCell>
          </TableRow>
        ))}
    </TableBody>
  </Table>
);

// Grid Skeleton for card layouts
export const GridSkeleton = ({
  SkeletonComponent,
  count = 6,
  columns = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
}: {
  SkeletonComponent: React.ComponentType;
  count?: number;
  columns?: string;
}) => (
  <div className={`grid ${columns} gap-2`}>
    {Array(count)
      .fill(0)
      .map((_, index) => (
        <SkeletonComponent key={index} />
      ))}
  </div>
);
