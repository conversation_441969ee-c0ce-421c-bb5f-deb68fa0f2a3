import Select from 'react-select';
import { ChevronDownIcon, X } from 'lucide-react';
import c from 'classnames';
import {
  ClearIndicatorProps,
  DropdownIndicatorProps,
  MultiValueRemoveProps,
  components,
} from 'react-select';

const DropdownIndicator = (props: DropdownIndicatorProps) => {
  return (
    <components.DropdownIndicator {...props}>
      <ChevronDownIcon size={15} />
    </components.DropdownIndicator>
  );
};

const ClearIndicator = (props: ClearIndicatorProps) => {
  return (
    <components.ClearIndicator {...props}>
      <X size={15} />
    </components.ClearIndicator>
  );
};

const MultiValueRemove = (props: MultiValueRemoveProps) => {
  return (
    <components.MultiValueRemove {...props}>
      <X size={15} />
    </components.MultiValueRemove>
  );
};

const controlStyles = {
  base: 'border border-border rounded-lg bg-background hover:cursor-pointer hover:bg-secondary',
  focus: 'border-border ring-ring ring-primary-500',
  nonFocus: 'border-border',
};
const placeholderStyles = 'text-muted-foreground text-sm ml-1';
const selectInputStyles = 'text-foreground text-sm ml-1';
const valueContainerStyles = 'text-foreground text-sm';
const singleValueStyles = 'ml-1';
const multiValueStyles =
  'ml-1 bg-background border border-border rounded items-center py-0.5 pl-2 pr-1 gap-1.5';
const multiValueLabelStyles = 'leading-6 py-0.5';
const multiValueRemoveStyles =
  'border border-gray-200 bg-white hover:bg-red-50 hover:text-red-800 text-gray-500 hover:border-red-300 rounded-md bg-background';
const indicatorsContainerStyles = 'p-1 gap-1 bg-background rounded-lg';
const clearIndicatorStyles = 'text-gray-500 p-1 rounded-md hover:text-red-800';
const indicatorSeparatorStyles = 'bg-mutated';
const dropdownIndicatorStyles = 'p-1 hover:text-foreground text-gray-500';
const menuStyles =
  'mt-2 p-2 border border-border bg-background text-sm rounded-lg';
const optionsStyle =
  'bg-background p-2 border-0 text-base hover:bg-secondary hover:cursor-pointer';
const groupHeadingStyles = 'ml-3 mt-2 mb-1 text-gray-500 text-sm bg-background';
const noOptionsMessageStyles = 'text-muted-foreground bg-background';

export type AppSelectOption = {
  value: string;
  label: string;
};

export type AppSelectOptionGroup = {
  label: string;
  options: AppSelectOption[];
};

type AppSelectProps =
  | {
      options: AppSelectOption[] | AppSelectOptionGroup[];
      placeholder: string;
      onChange: (value: string[]) => void;
      value?: string[];
      multiple: true;
      components?: any;
    }
  | {
      options: AppSelectOption[] | AppSelectOptionGroup[];
      placeholder: string;
      onChange: (value: string) => void;
      value?: string;
      multiple?: false;
      components?: any;
    };

export const AppSelect = ({
  options,
  placeholder,
  onChange,
  multiple = false,
  value = '',
  components,
}: AppSelectProps) => {
  const onSelectChange = (newValue) => {
    const finalValue = Array.isArray(newValue)
      ? newValue.map((v) => v.value)
      : newValue?.value;

    onChange(finalValue);
  };

  const internalOptions =
    options[0] && Array.isArray((options[0] as AppSelectOptionGroup)?.options)
      ? options.map((option) => option.options).flat()
      : options;

  const selectedValue = multiple
    ? internalOptions.filter((option) => value.includes(option.value as string))
    : internalOptions.find((option) => option.value === value);

  // const isDarkMode = document.documentElement.classList.contains('dark');

  return (
    <Select
      unstyled
      isMulti={multiple}
      options={options as any[]}
      value={selectedValue || ''}
      components={{
        ...components,
        MultiValueRemove,
        ClearIndicator,
        DropdownIndicator,
      }}
      isClearable
      classNames={{
        control: ({ isFocused }) =>
          c(
            isFocused ? controlStyles.focus : controlStyles.nonFocus,
            controlStyles.base,
          ),
        placeholder: () => placeholderStyles,
        input: () => selectInputStyles,
        option: () => optionsStyle,
        menu: () => menuStyles,
        valueContainer: () => valueContainerStyles,
        singleValue: () => singleValueStyles,
        multiValue: () => multiValueStyles,
        multiValueLabel: () => multiValueLabelStyles,
        multiValueRemove: () => multiValueRemoveStyles,
        indicatorsContainer: () => indicatorsContainerStyles,
        clearIndicator: () => clearIndicatorStyles,
        indicatorSeparator: () => indicatorSeparatorStyles,
        dropdownIndicator: () => dropdownIndicatorStyles,
        groupHeading: () => groupHeadingStyles,
        noOptionsMessage: () => noOptionsMessageStyles,
      }}
      onChange={onSelectChange}
      placeholder={placeholder}
    />
  );
};
