import { useMemo } from 'react';
import { useRootContext } from '../../context/useRootContext';
import { AppSelect } from './AppSelect';
import { OptionProps, components } from 'react-select';

const Control = ({ children, ...props }: any) => {
  const authorId = props.getValue()[0]?.value;

  return (
    <components.Control {...props}>
      <div className='flex gap-2 items-center pl-2 w-[100%] bg-transparent'>
        <SelectAvatarUrl {...{ authorId }} />
        {children}
      </div>
    </components.Control>
  );
};

const Option = (props: OptionProps<any>) => {
  const authorId = props.data.value;

  return (
    <components.Option {...props}>
      <div className='flex gap-2 items-center w-[100%] bg-transparent'>
        <SelectAvatarUrl {...{ authorId }} />
        {props.children}
      </div>
    </components.Option>
  );
};

type AppAuthorSelectProps = {
  className?: string;
  value?: string;
  onChange(value: string): void;
};

export const SelectAvatarUrl = ({ authorId }: { authorId?: string }) => {
  const { authors } = useRootContext();

  if (!authorId) return '';

  const avatarUrl =
    authors.find((author) => author.id === authorId)?.avatarUrl || '';

  return avatarUrl ? (
    <img
      src={avatarUrl}
      alt='no logo'
      className='rounded-[50%] object-cover w-[20px] h-[20px]'
    />
  ) : (
    ''
  );
};

export const AppAuthorSelect = ({ value, onChange }: AppAuthorSelectProps) => {
  const { authors } = useRootContext();

  const authorSelectOptions = useMemo(
    () => authors.map(({ id, name }) => ({ value: id, label: name })),
    [authors],
  );

  return (
    <div className='py-3 w-full'>
      <AppSelect
        placeholder='Select author'
        value={value}
        options={authorSelectOptions}
        onChange={onChange}
        components={{ Control, Option }}
      />
    </div>
  );
};
