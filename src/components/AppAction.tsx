import c from 'classnames';
import noop from 'lodash/noop';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

type AppActionProps = {
  onClick?: any;
  disabled?: any;
  children: any;
  className?: string;
  width?: number;
  tooltip?: string;
};

export const AppAction = ({
  onClick = noop,
  children,
  className,
  disabled,
  tooltip,
  width = 50,
}: AppActionProps) => {
  const combinedClassName = c(
    `flex justify-start items-center gap-2 rounded-[10px] text-[25px] duration-150 w-[${width}px] h-[40px] hover:border-black`,
    className,
  );

  const button = (
    <button className={combinedClassName} onClick={onClick} disabled={disabled}>
      {children}
    </button>
  );

  if (!tooltip) {
    return button;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{button}</TooltipTrigger>
        <TooltipContent>{tooltip}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
