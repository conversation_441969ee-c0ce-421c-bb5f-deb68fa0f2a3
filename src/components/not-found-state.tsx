export const NotFoundState = ({ title }: { title: string }) => {
  return (
    <div className='flex flex-col items-center justify-center min-h-[100dvh] px-4 md:px-6 py-12 md:py-24 bg-gray-100 dark:bg-gray-900 w-full h-full'>
      <div className='max-w-md text-center space-y-4'>
        <FrownIcon className='mx-auto h-24 w-24 text-gray-500 dark:text-gray-400' />
        <h1 className='text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl'>
          Oops!
        </h1>
        <p className='text-gray-500 dark:text-gray-400 md:text-xl'>{title}</p>
        <a
          href='/'
          className='inline-flex h-10 items-center justify-center rounded-md bg-gray-900 px-8 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300'
        >
          Go back home
        </a>
      </div>
    </div>
  );
};

function FrownIcon(props) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <circle cx='12' cy='12' r='10' />
      <path d='M16 16s-1.5-2-4-2-4 2-4 2' />
      <line x1='9' x2='9.01' y1='9' y2='9' />
      <line x1='15' x2='15.01' y1='9' y2='9' />
    </svg>
  );
}
