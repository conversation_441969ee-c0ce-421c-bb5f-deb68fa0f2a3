import { useState } from 'react';
import {
  ChevronUp,
  FileText,
  Video,
  Twitter,
  Github,
  Globe,
} from 'lucide-react';
import { Drawer } from 'vaul';
import { AppResourceType } from '@/core.types';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useRootContext } from '@/context/useRootContext';

interface MobileCategoryPillProps {
  currentCategory: AppResourceType | 'proposal';
  onCategoryChange: (category: AppResourceType | 'proposal') => void;
  className?: string;
}

const getCategoryIcon = (category: AppResourceType | 'proposal') => {
  const iconProps = { className: 'h-4 w-4' };

  switch (category) {
    case AppResourceType.Article:
      return <FileText {...iconProps} />;
    case AppResourceType.Video:
      return <Video {...iconProps} />;
    case AppResourceType.Tweet:
      return <Twitter {...iconProps} />;
    case AppResourceType.Github:
      return <Github {...iconProps} />;
    case AppResourceType.Sites:
      return <Globe {...iconProps} />;
    case AppResourceType.Papers:
      return <FileText {...iconProps} />;
    case 'proposal':
      return <FileText {...iconProps} />;
    default:
      return <FileText {...iconProps} />;
  }
};

const getCategoryLabel = (category: AppResourceType | 'proposal') => {
  switch (category) {
    case AppResourceType.Article:
      return 'Articles';
    case AppResourceType.Video:
      return 'Videos';
    case AppResourceType.Tweet:
      return 'Tweets';
    case AppResourceType.Github:
      return 'GitHub';
    case AppResourceType.Sites:
      return 'Sites';
    case AppResourceType.Papers:
      return 'Papers';
    case 'proposal':
      return 'My Proposals';
    default:
      return 'Articles';
  }
};

const CategoryOption = ({
  category,
  isSelected,
  onClick,
}: {
  category: AppResourceType | 'proposal';
  isSelected: boolean;
  onClick: () => void;
}) => {
  return (
    <Button
      variant={isSelected ? 'default' : 'ghost'}
      className={cn(
        'w-full justify-start h-12 text-base font-medium',
        isSelected && 'bg-primary text-primary-foreground',
      )}
      onClick={onClick}
    >
      {getCategoryIcon(category)}
      <span className='ml-3'>{getCategoryLabel(category)}</span>
    </Button>
  );
};

export const MobileCategoryPill = ({
  currentCategory,
  onCategoryChange,
  className,
}: MobileCategoryPillProps) => {
  const [open, setOpen] = useState(false);
  const { isAuthorized } = useRootContext();

  const categories: (AppResourceType | 'proposal')[] = [
    AppResourceType.Article,
    AppResourceType.Video,
    AppResourceType.Tweet,
    AppResourceType.Github,
    AppResourceType.Papers,
    AppResourceType.Sites,
    ...(isAuthorized ? ['proposal' as const] : []),
  ];

  const handleCategorySelect = (category: AppResourceType | 'proposal') => {
    onCategoryChange(category);
    setOpen(false);
  };

  return (
    <Drawer.Root open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <div
          className={cn(
            'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
            'bg-background border border-border rounded-full shadow-lg',
            'px-4 py-3 flex items-center gap-2 cursor-pointer',
            'hover:shadow-xl transition-shadow duration-200',
            'backdrop-blur-sm bg-background/95',
            className,
          )}
        >
          {getCategoryIcon(currentCategory)}
          <span className='font-medium text-sm'>
            {getCategoryLabel(currentCategory)}
          </span>
          <ChevronUp className='h-4 w-4 ml-1' />
        </div>
      </Drawer.Trigger>

      <Drawer.Portal>
        <Drawer.Overlay className='fixed inset-0 bg-black/40' />
        <Drawer.Content className='bg-background flex flex-col rounded-t-[10px] h-[75vh] fixed bottom-0 left-0 right-0 outline-none'>
          <div className='p-4 bg-background rounded-t-[10px] flex-1'>
            <div className='mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted mb-6' />

            <div className='mb-4'>
              <h3 className='text-lg font-semibold text-center'>
                Select Category
              </h3>
              <p className='text-sm text-muted-foreground text-center mt-1'>
                Choose a resource type to explore
              </p>
            </div>

            <div className='space-y-2 max-h-[60vh] overflow-y-auto'>
              {categories.map((category) => (
                <CategoryOption
                  key={category}
                  category={category}
                  isSelected={category === currentCategory}
                  onClick={() => handleCategorySelect(category)}
                />
              ))}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
