import { useState } from 'react';
import { Filter } from 'lucide-react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { AppAuthorSelect } from '@/components/AppSelect/AppAuthorSelect';
import { BookmarksToggle } from '@/components/BookmarksToggle';
import { SearchTags } from '@/search-page/search-tags';
import { useSearchFiltersContext } from '@/search-page/search-filters-context';
import { useAppResourcesContext } from '@/search-page/app-resources-context';
import { useRootContext } from '@/context/useRootContext';
import { AppButtonSecondary } from '../buttons/app-button-new';

interface MobileFilterDrawerProps {
  className?: string;
}

export const MobileFilterDrawer = ({ className }: MobileFilterDrawerProps) => {
  const [open, setOpen] = useState(false);
  const { authorId, setAuthorID } = useSearchFiltersContext();
  const {
    resetAndReloadResources,
    bookmarksMode,
    showBookMarks,
    hideBookMarks,
  } = useAppResourcesContext();
  const { isAuthorized } = useRootContext();

  const onAuthorSelectChange = (authorId: string) => {
    setAuthorID(authorId);
    resetAndReloadResources();
  };

  const onBookmarkSwitchChange = (value: boolean) => {
    if (value) {
      showBookMarks();
    } else {
      hideBookMarks();
    }
  };

  return (
    <Drawer.Root open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <AppButtonSecondary className={className}>
          <Filter className='h-4 w-4' />
        </AppButtonSecondary>
      </Drawer.Trigger>

      <Drawer.Portal>
        <Drawer.Overlay className='fixed inset-0 bg-black/40' />
        <Drawer.Content
          className='z-[100] bg-background flex flex-col rounded-t-[10px] h-[90vh] fixed bottom-0 left-0 right-0 outline-none'
          {...({} as any)}
        >
          <div className='p-4 bg-background rounded-t-[10px] flex-1 overflow-hidden'>
            <div className='mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted mb-6' />

            <div className='space-y-6 overflow-y-auto flex-1'>
              <AppAuthorSelect
                value={authorId}
                onChange={onAuthorSelectChange}
              />

              {isAuthorized && (
                <BookmarksToggle
                  isActive={bookmarksMode}
                  onToggle={onBookmarkSwitchChange}
                  className='w-full'
                />
              )}

              <div className='space-y-3'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Tags
                </h4>
                <div className='border rounded-lg p-3 bg-muted/20'>
                  <SearchTags
                    style={{ maxHeight: '400px', overflowY: 'auto' }}
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className='flex gap-3 pt-4 border-t mt-4'>
              <Button
                variant='outline'
                className='flex-1'
                onClick={() => setOpen(false)}
              >
                Close
              </Button>
              <Button
                className='flex-1'
                onClick={() => {
                  resetAndReloadResources();
                  setOpen(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
