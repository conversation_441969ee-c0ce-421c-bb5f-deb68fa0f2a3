import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface RadioGroupProps {
  label: string;
  options: {
    label: string;
    value: string;
  }[];
  value: string;
  onChange: (value: string) => void;
}

export const AppRadioGroup = ({
  options,
  label,
  value,
  onChange,
}: RadioGroupProps) => {
  return (
    <div className='flex flex-wrap gap-2 items-start mb-3 pl-1'>
      <Label className='mb-2 text-md'>{label}</Label>

      <RadioGroup
        value={value}
        className='flex flex-wrap gap-2 w-full'
        onValueChange={onChange}
      >
        {options.map((option) => (
          <div
            className='flex items-center space-x-2 cursor-pointer'
            key={option.value}
          >
            <RadioGroupItem value={option.value} id={option.value} />
            <Label htmlFor={option.value} className='cursor-pointer'>
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};
